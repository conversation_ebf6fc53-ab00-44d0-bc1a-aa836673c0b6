"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/settings/general/route";
exports.ids = ["app/api/settings/general/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fgeneral%2Froute&page=%2Fapi%2Fsettings%2Fgeneral%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fgeneral%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fgeneral%2Froute&page=%2Fapi%2Fsettings%2Fgeneral%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fgeneral%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_settings_general_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/settings/general/route.ts */ \"(rsc)/./src/app/api/settings/general/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/settings/general/route\",\n        pathname: \"/api/settings/general\",\n        filename: \"route\",\n        bundlePath: \"app/api/settings/general/route\"\n    },\n    resolvedPagePath: \"D:\\\\CURSOR-AI\\\\ai-project\\\\TintPlatesStdio\\\\src\\\\app\\\\api\\\\settings\\\\general\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_settings_general_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/settings/general/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZzZXR0aW5ncyUyRmdlbmVyYWwlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnNldHRpbmdzJTJGZ2VuZXJhbCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnNldHRpbmdzJTJGZ2VuZXJhbCUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDQ1VSU09SLUFJJTVDYWktcHJvamVjdCU1Q1RpbnRQbGF0ZXNTdGRpbyU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q0NVUlNPUi1BSSU1Q2FpLXByb2plY3QlNUNUaW50UGxhdGVzU3RkaW8maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDc0M7QUFDbkg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1R0FBdUc7QUFDL0c7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUM2Sjs7QUFFN0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aW50cGxhdGVzc3RkaW8vPzFkMmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcQ1VSU09SLUFJXFxcXGFpLXByb2plY3RcXFxcVGludFBsYXRlc1N0ZGlvXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHNldHRpbmdzXFxcXGdlbmVyYWxcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3NldHRpbmdzL2dlbmVyYWwvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zZXR0aW5ncy9nZW5lcmFsXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9zZXR0aW5ncy9nZW5lcmFsL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxcQ1VSU09SLUFJXFxcXGFpLXByb2plY3RcXFxcVGludFBsYXRlc1N0ZGlvXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHNldHRpbmdzXFxcXGdlbmVyYWxcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgaGVhZGVySG9va3MsIHN0YXRpY0dlbmVyYXRpb25CYWlsb3V0IH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvc2V0dGluZ3MvZ2VuZXJhbC9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fgeneral%2Froute&page=%2Fapi%2Fsettings%2Fgeneral%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fgeneral%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/settings/general/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/settings/general/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_dbConnect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/dbConnect */ \"(rsc)/./src/lib/dbConnect.ts\");\n/* harmony import */ var _models_Settings__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Settings */ \"(rsc)/./src/models/Settings.ts\");\n\n\n\n\n\nasync function GET() {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        await (0,_lib_dbConnect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const settings = await _models_Settings__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOne({\n            type: \"general\"\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            settings: settings || {}\n        });\n    } catch (error) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Ayarlar y\\xfcklenirken hata oluştu\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        await (0,_lib_dbConnect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const body = await request.json();\n        const settings = await _models_Settings__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOneAndUpdate({\n            type: \"general\"\n        }, {\n            $set: {\n                companyName: body.companyName,\n                address: body.address,\n                phone: body.phone,\n                email: body.email,\n                taxNumber: body.taxNumber,\n                currency: body.currency || \"TRY\",\n                timezone: body.timezone || \"Europe/Istanbul\",\n                language: body.language || \"tr\",\n                updatedAt: new Date()\n            }\n        }, {\n            new: true,\n            upsert: true,\n            setDefaultsOnInsert: true\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            settings\n        });\n    } catch (error) {\n        console.error(\"Genel ayarlar g\\xfcncelleme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Ayarlar g\\xfcncellenirken hata oluştu\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/settings/general/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"E-posta\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Şifre\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"E-posta ve şifre gerekli\");\n                    }\n                    const client = await _mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n                    const db = client.db(\"pilatesstudio\");\n                    // Önce users koleksiyonunda ara (admin için)\n                    let user = await db.collection(\"users\").findOne({\n                        email: credentials.email,\n                        isActive: true\n                    });\n                    // Kullanıcı bulunamazsa öğretmenler koleksiyonunda ara\n                    if (!user) {\n                        user = await db.collection(\"teachers\").findOne({\n                            email: credentials.email,\n                            isActive: true\n                        });\n                    }\n                    if (!user) {\n                        throw new Error(\"Kullanıcı bulunamadı\");\n                    }\n                    const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isValid) {\n                        throw new Error(\"Hatalı şifre\");\n                    }\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role || \"teacher\",\n                        color: user.color // Öğretmenler için renk kodu\n                    };\n                } catch (error) {\n                    throw error;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                if (user.color) token.color = user.color; // Öğretmen renk kodu\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                if (token.color) session.user.color = token.color; // Öğretmen renk kodu\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/login\"\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/dbConnect.ts":
/*!******************************!*\
  !*** ./src/lib/dbConnect.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış\");\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function dbConnect() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n// Bağlantıyı test et\nif (true) {\n    dbConnect().then(()=>console.log(\"Test bağlantısı başarılı\")).catch((error)=>console.error(\"Test bağlantısı başarısız:\", error));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/dbConnect.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış.\");\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {};\nlet client;\nlet clientPromise;\nif (true) {\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Settings.ts":
/*!********************************!*\
  !*** ./src/models/Settings.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst settingsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    type: {\n        type: String,\n        required: true,\n        enum: [\n            \"general\",\n            \"branch\",\n            \"notification\",\n            \"backup\"\n        ]\n    },\n    companyName: String,\n    address: String,\n    phone: String,\n    email: String,\n    taxNumber: String,\n    currency: {\n        type: String,\n        default: \"TRY\"\n    },\n    timezone: {\n        type: String,\n        default: \"Europe/Istanbul\"\n    },\n    language: {\n        type: String,\n        default: \"tr\"\n    },\n    // Branch ayarları\n    defaultWorkingHours: {\n        start: String,\n        end: String\n    },\n    maxRoomCapacity: Number,\n    // Bildirim ayarları\n    emailNotifications: Boolean,\n    paymentReminders: Boolean,\n    lessonReminders: Boolean,\n    // Yedekleme ayarları\n    autoBackup: Boolean,\n    backupFrequency: String,\n    backupTime: String,\n    retentionDays: Number,\n    // Genel alanlar\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedAt: Date\n});\n// Benzersiz type alanı için index\nsettingsSchema.index({\n    type: 1\n}, {\n    unique: true\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Settings || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Settings\", settingsSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Settings.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/bcryptjs","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fgeneral%2Froute&page=%2Fapi%2Fsettings%2Fgeneral%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fgeneral%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();