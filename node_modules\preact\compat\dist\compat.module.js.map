{"version": 3, "file": "compat.module.js", "sources": ["../src/util.js", "../src/PureComponent.js", "../src/memo.js", "../src/forwardRef.js", "../src/Children.js", "../src/suspense.js", "../src/suspense-list.js", "../../src/constants.js", "../src/portals.js", "../src/render.js", "../src/index.js"], "sourcesContent": ["/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Check if two objects have a different shape\n * @param {object} a\n * @param {object} b\n * @returns {boolean}\n */\nexport function shallowDiffers(a, b) {\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\n\treturn false;\n}\n\n/**\n * Check if two values are the same value\n * @param {*} x\n * @param {*} y\n * @returns {boolean}\n */\nexport function is(x, y) {\n\treturn (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\n", "import { Component } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Component class with a predefined `shouldComponentUpdate` implementation\n */\nexport function PureComponent(p, c) {\n\tthis.props = p;\n\tthis.context = c;\n}\nPureComponent.prototype = new Component();\n// Some third-party libraries check if this property is present\nPureComponent.prototype.isPureReactComponent = true;\nPureComponent.prototype.shouldComponentUpdate = function (props, state) {\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\n};\n", "import { createElement } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Memoize a component, so that it only updates when the props actually have\n * changed. This was previously known as `React.pure`.\n * @param {import('./internal').FunctionComponent} c functional component\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\n * @returns {import('./internal').FunctionComponent}\n */\nexport function memo(c, comparer) {\n\tfunction shouldUpdate(nextProps) {\n\t\tlet ref = this.props.ref;\n\t\tlet updateRef = ref == nextProps.ref;\n\t\tif (!updateRef && ref) {\n\t\t\tref.call ? ref(null) : (ref.current = null);\n\t\t}\n\n\t\tif (!comparer) {\n\t\t\treturn shallowDiffers(this.props, nextProps);\n\t\t}\n\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\n\t}\n\n\tfunction Memoed(props) {\n\t\tthis.shouldComponentUpdate = shouldUpdate;\n\t\treturn createElement(c, props);\n\t}\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\n\tMemoed.prototype.isReactComponent = true;\n\tMemoed._forwarded = true;\n\treturn Memoed;\n}\n", "import { options } from 'preact';\n\nlet oldDiffHook = options._diff;\noptions._diff = vnode => {\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\n\t\tvnode.props.ref = vnode.ref;\n\t\tvnode.ref = null;\n\t}\n\tif (oldDiffHook) oldDiffHook(vnode);\n};\n\nexport const REACT_FORWARD_SYMBOL =\n\t(typeof Symbol != 'undefined' &&\n\t\tSymbol.for &&\n\t\tSymbol.for('react.forward_ref')) ||\n\t0xf47;\n\n/**\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\n * wrap components. Using `forwardRef` there is an easy way to get a reference\n * of the wrapped component instead of one of the wrapper itself.\n * @param {import('./index').ForwardFn} fn\n * @returns {import('./internal').FunctionComponent}\n */\nexport function forwardRef(fn) {\n\tfunction Forwarded(props) {\n\t\tif (!('ref' in props)) return fn(props, null);\n\n\t\tlet ref = props.ref;\n\t\tdelete props.ref;\n\t\tconst result = fn(props, ref);\n\t\tprops.ref = ref;\n\t\treturn result;\n\t}\n\n\t// mobx-react checks for this being present\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\n\t// mobx-react heavily relies on implementation details.\n\t// It expects an object here with a `render` property,\n\t// and prototype.render will fail. Without this\n\t// mobx-react throws.\n\tForwarded.render = Forwarded;\n\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\n\treturn Forwarded;\n}\n", "import { toChildArray } from 'preact';\n\nconst mapFn = (children, fn) => {\n\tif (children == null) return null;\n\treturn toChildArray(toChildArray(children).map(fn));\n};\n\n// This API is completely unnecessary for Preact, so it's basically passthrough.\nexport const Children = {\n\tmap: mapFn,\n\tforEach: mapFn,\n\tcount(children) {\n\t\treturn children ? toChildArray(children).length : 0;\n\t},\n\tonly(children) {\n\t\tconst normalized = toChildArray(children);\n\t\tif (normalized.length !== 1) throw 'Children.only';\n\t\treturn normalized[0];\n\t},\n\ttoArray: toChildArray\n};\n", "import { Component, createElement, options, Fragment } from 'preact';\nimport { MODE_HYDRATE } from '../../src/constants';\nimport { assign } from './util';\n\nconst oldCatchError = options._catchError;\noptions._catchError = function (error, newVNode, oldVNode, errorInfo) {\n\tif (error.then) {\n\t\t/** @type {import('./internal').Component} */\n\t\tlet component;\n\t\tlet vnode = newVNode;\n\n\t\tfor (; (vnode = vnode._parent); ) {\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\n\t\t\t\tif (newVNode._dom == null) {\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t}\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\n\t\t\t}\n\t\t}\n\t}\n\toldCatchError(error, newVNode, oldVNode, errorInfo);\n};\n\nconst oldUnmount = options.unmount;\noptions.unmount = function (vnode) {\n\t/** @type {import('./internal').Component} */\n\tconst component = vnode._component;\n\tif (component && component._onResolve) {\n\t\tcomponent._onResolve();\n\t}\n\n\t// if the component is still hydrating\n\t// most likely it is because the component is suspended\n\t// we set the vnode.type as `null` so that it is not a typeof function\n\t// so the unmount will remove the vnode._dom\n\tif (component && vnode._flags & MODE_HYDRATE) {\n\t\tvnode.type = null;\n\t}\n\n\tif (oldUnmount) oldUnmount(vnode);\n};\n\nfunction detachedClone(vnode, detachedParent, parentDom) {\n\tif (vnode) {\n\t\tif (vnode._component && vnode._component.__hooks) {\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\n\t\t\t});\n\n\t\t\tvnode._component.__hooks = null;\n\t\t}\n\n\t\tvnode = assign({}, vnode);\n\t\tif (vnode._component != null) {\n\t\t\tif (vnode._component._parentDom === parentDom) {\n\t\t\t\tvnode._component._parentDom = detachedParent;\n\t\t\t}\n\t\t\tvnode._component = null;\n\t\t}\n\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\n\t\t\t);\n\t}\n\n\treturn vnode;\n}\n\nfunction removeOriginal(vnode, detachedParent, originalParent) {\n\tif (vnode && originalParent) {\n\t\tvnode._original = null;\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\n\t\t\t);\n\n\t\tif (vnode._component) {\n\t\t\tif (vnode._component._parentDom === detachedParent) {\n\t\t\t\tif (vnode._dom) {\n\t\t\t\t\toriginalParent.appendChild(vnode._dom);\n\t\t\t\t}\n\t\t\t\tvnode._component._force = true;\n\t\t\t\tvnode._component._parentDom = originalParent;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn vnode;\n}\n\n// having custom inheritance instead of a class here saves a lot of bytes\nexport function Suspense() {\n\t// we do not call super here to golf some bytes...\n\tthis._pendingSuspensionCount = 0;\n\tthis._suspenders = null;\n\tthis._detachOnNextRender = null;\n}\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspense.prototype = new Component();\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {Promise} promise The thrown promise\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\n */\nSuspense.prototype._childDidSuspend = function (promise, suspendingVNode) {\n\tconst suspendingComponent = suspendingVNode._component;\n\n\t/** @type {import('./internal').SuspenseComponent} */\n\tconst c = this;\n\n\tif (c._suspenders == null) {\n\t\tc._suspenders = [];\n\t}\n\tc._suspenders.push(suspendingComponent);\n\n\tconst resolve = suspended(c._vnode);\n\n\tlet resolved = false;\n\tconst onResolved = () => {\n\t\tif (resolved) return;\n\n\t\tresolved = true;\n\t\tsuspendingComponent._onResolve = null;\n\n\t\tif (resolve) {\n\t\t\tresolve(onSuspensionComplete);\n\t\t} else {\n\t\t\tonSuspensionComplete();\n\t\t}\n\t};\n\n\tsuspendingComponent._onResolve = onResolved;\n\n\tconst onSuspensionComplete = () => {\n\t\tif (!--c._pendingSuspensionCount) {\n\t\t\t// If the suspension was during hydration we don't need to restore the\n\t\t\t// suspended children into the _children array\n\t\t\tif (c.state._suspended) {\n\t\t\t\tconst suspendedVNode = c.state._suspended;\n\t\t\t\tc._vnode._children[0] = removeOriginal(\n\t\t\t\t\tsuspendedVNode,\n\t\t\t\t\tsuspendedVNode._component._parentDom,\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\n\n\t\t\tlet suspended;\n\t\t\twhile ((suspended = c._suspenders.pop())) {\n\t\t\t\tsuspended.forceUpdate();\n\t\t\t}\n\t\t}\n\t};\n\n\t/**\n\t * We do not set `suspended: true` during hydration because we want the actual markup\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\n\t */\n\tif (\n\t\t!c._pendingSuspensionCount++ &&\n\t\t!(suspendingVNode._flags & MODE_HYDRATE)\n\t) {\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\n\t}\n\tpromise.then(onResolved, onResolved);\n};\n\nSuspense.prototype.componentWillUnmount = function () {\n\tthis._suspenders = [];\n};\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\n * @param {import('./internal').SuspenseState} state\n */\nSuspense.prototype.render = function (props, state) {\n\tif (this._detachOnNextRender) {\n\t\t// When the Suspense's _vnode was created by a call to createVNode\n\t\t// (i.e. due to a setState further up in the tree)\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\n\t\tif (this._vnode._children) {\n\t\t\tconst detachedParent = document.createElement('div');\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\n\t\t\tthis._vnode._children[0] = detachedClone(\n\t\t\t\tthis._detachOnNextRender,\n\t\t\t\tdetachedParent,\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\n\t\t\t);\n\t\t}\n\n\t\tthis._detachOnNextRender = null;\n\t}\n\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\n\t/** @type {import('./internal').VNode} */\n\tconst fallback =\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\n\tif (fallback) fallback._flags &= ~MODE_HYDRATE;\n\n\treturn [\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\n\t\tfallback\n\t];\n};\n\n/**\n * Checks and calls the parent component's _suspended method, passing in the\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\n * that one of its children/descendants suspended.\n *\n * The parent MAY return a callback. The callback will get called when the\n * suspension resolves, notifying the parent of the fact.\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\n * This is a way for the parent to delay unsuspending.\n *\n * If the parent does not return a callback then the resolved vnode\n * gets unsuspended immediately when it resolves.\n *\n * @param {import('./internal').VNode} vnode\n * @returns {((unsuspend: () => void) => void)?}\n */\nexport function suspended(vnode) {\n\t/** @type {import('./internal').Component} */\n\tlet component = vnode._parent._component;\n\treturn component && component._suspended && component._suspended(vnode);\n}\n\nexport function lazy(loader) {\n\tlet prom;\n\tlet component;\n\tlet error;\n\n\tfunction Lazy(props) {\n\t\tif (!prom) {\n\t\t\tprom = loader();\n\t\t\tprom.then(\n\t\t\t\texports => {\n\t\t\t\t\tcomponent = exports.default || exports;\n\t\t\t\t},\n\t\t\t\te => {\n\t\t\t\t\terror = e;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (!component) {\n\t\t\tthrow prom;\n\t\t}\n\n\t\treturn createElement(component, props);\n\t}\n\n\tLazy.displayName = 'Lazy';\n\tLazy._forwarded = true;\n\treturn Lazy;\n}\n", "import { Component, toChildArray } from 'preact';\nimport { suspended } from './suspense.js';\n\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\nconst SUSPENDED_COUNT = 0;\nconst RESOLVED_COUNT = 1;\nconst NEXT_NODE = 2;\n\n// Having custom inheritance instead of a class here saves a lot of bytes.\nexport function SuspenseList() {\n\tthis._next = null;\n\tthis._map = null;\n}\n\n// Mark one of child's earlier suspensions as resolved.\n// Some pending callbacks may become callable due to this\n// (e.g. the last suspended descendant gets resolved when\n// revealOrder === 'together'). Process those callbacks as well.\nconst resolve = (list, child, node) => {\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\n\t\t// The number a child (or any of its descendants) has been suspended\n\t\t// matches the number of times it's been resolved. Therefore we\n\t\t// mark the child as completely resolved by deleting it from ._map.\n\t\t// This is used to figure out when *all* children have been completely\n\t\t// resolved when revealOrder is 'together'.\n\t\tlist._map.delete(child);\n\t}\n\n\t// If revealOrder is falsy then we can do an early exit, as the\n\t// callbacks won't get queued in the node anyway.\n\t// If revealOrder is 'together' then also do an early exit\n\t// if all suspended descendants have not yet been resolved.\n\tif (\n\t\t!list.props.revealOrder ||\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\n\t) {\n\t\treturn;\n\t}\n\n\t// Walk the currently suspended children in order, calling their\n\t// stored callbacks on the way. Stop if we encounter a child that\n\t// has not been completely resolved yet.\n\tnode = list._next;\n\twhile (node) {\n\t\twhile (node.length > 3) {\n\t\t\tnode.pop()();\n\t\t}\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\n\t\t\tbreak;\n\t\t}\n\t\tlist._next = node = node[NEXT_NODE];\n\t}\n};\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspenseList.prototype = new Component();\n\nSuspenseList.prototype._suspended = function (child) {\n\tconst list = this;\n\tconst delegated = suspended(list._vnode);\n\n\tlet node = list._map.get(child);\n\tnode[SUSPENDED_COUNT]++;\n\n\treturn unsuspend => {\n\t\tconst wrappedUnsuspend = () => {\n\t\t\tif (!list.props.revealOrder) {\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\n\t\t\t\tunsuspend();\n\t\t\t} else {\n\t\t\t\tnode.push(unsuspend);\n\t\t\t\tresolve(list, child, node);\n\t\t\t}\n\t\t};\n\t\tif (delegated) {\n\t\t\tdelegated(wrappedUnsuspend);\n\t\t} else {\n\t\t\twrappedUnsuspend();\n\t\t}\n\t};\n};\n\nSuspenseList.prototype.render = function (props) {\n\tthis._next = null;\n\tthis._map = new Map();\n\n\tconst children = toChildArray(props.children);\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\n\t\t// then flip the child list around so that the last child will be\n\t\t// the first in the linked list.\n\t\tchildren.reverse();\n\t}\n\t// Build the linked list. Iterate through the children in reverse order\n\t// so that `_next` points to the first linked list node to be resolved.\n\tfor (let i = children.length; i--; ) {\n\t\t// Create a new linked list node as an array of form:\n\t\t// \t[suspended_count, resolved_count, next_node]\n\t\t// where suspended_count and resolved_count are numeric counters for\n\t\t// keeping track how many times a node has been suspended and resolved.\n\t\t//\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\n\t\t// processing callbacks until componentDidMount has been called. In a sense\n\t\t// node is suspended at least until componentDidMount gets called!\n\t\t//\n\t\t// Pending callbacks are added to the end of the node:\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\n\t}\n\treturn props.children;\n};\n\nSuspenseList.prototype.componentDidUpdate =\n\tSuspenseList.prototype.componentDidMount = function () {\n\t\t// Iterate through all children after mounting for two reasons:\n\t\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\n\t\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\n\t\t//    The nodes can now be completely consumed from the linked list.\n\t\t// 2. Handle nodes that might have gotten resolved between render and\n\t\t//    componentDidMount.\n\t\tthis._map.forEach((node, child) => {\n\t\t\tresolve(this, child, node);\n\t\t});\n\t};\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { createElement, render } from 'preact';\n\n/**\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\n */\nfunction ContextProvider(props) {\n\tthis.getChildContext = () => props.context;\n\treturn props.children;\n}\n\n/**\n * Portal component\n * @this {import('./internal').Component}\n * @param {object | null | undefined} props\n *\n * TODO: use createRoot() instead of fake root\n */\nfunction Portal(props) {\n\tconst _this = this;\n\tlet container = props._container;\n\n\t_this.componentWillUnmount = function () {\n\t\trender(null, _this._temp);\n\t\t_this._temp = null;\n\t\t_this._container = null;\n\t};\n\n\t// When we change container we should clear our old container and\n\t// indicate a new mount.\n\tif (_this._container && _this._container !== container) {\n\t\t_this.componentWillUnmount();\n\t}\n\n\tif (!_this._temp) {\n\t\t_this._container = container;\n\n\t\t// Create a fake DOM parent node that manages a subset of `container`'s children:\n\t\t_this._temp = {\n\t\t\tnodeType: 1,\n\t\t\tparentNode: container,\n\t\t\tchildNodes: [],\n\t\t\tcontains: () => true,\n\t\t\tappendChild(child) {\n\t\t\t\tthis.childNodes.push(child);\n\t\t\t\t_this._container.appendChild(child);\n\t\t\t},\n\t\t\tinsertBefore(child, before) {\n\t\t\t\tthis.childNodes.push(child);\n\t\t\t\t_this._container.appendChild(child);\n\t\t\t},\n\t\t\tremoveChild(child) {\n\t\t\t\tthis.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\n\t\t\t\t_this._container.removeChild(child);\n\t\t\t}\n\t\t};\n\t}\n\n\t// Render our wrapping element into temp.\n\trender(\n\t\tcreateElement(ContextProvider, { context: _this.context }, props._vnode),\n\t\t_this._temp\n\t);\n}\n\n/**\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\n * @param {import('./internal').VNode} vnode The vnode to render\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\n */\nexport function createPortal(vnode, container) {\n\tconst el = createElement(Portal, { _vnode: vnode, _container: container });\n\tel.containerInfo = container;\n\treturn el;\n}\n", "import {\n\trender as preactRender,\n\thydrate as preactHydrate,\n\toptions,\n\ttoChildArray,\n\tComponent\n} from 'preact';\nimport {\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tuseEffect,\n\tuseId,\n\tuseImperativeHandle,\n\tuseLayoutEffect,\n\tuseMemo,\n\tuseReducer,\n\tuseRef,\n\tuseState\n} from 'preact/hooks';\nimport {\n\tuseDeferredValue,\n\tuseInsertionEffect,\n\tuseSyncExternalStore,\n\tuseTransition\n} from './index';\n\nexport const REACT_ELEMENT_TYPE =\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\n\t0xeac7;\n\nconst CAMEL_PROPS =\n\t/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\nconst ON_ANI = /^on(Ani|Tra|Tou|BeforeInp|Compo)/;\nconst CAMEL_REPLACE = /[A-Z0-9]/g;\nconst IS_DOM = typeof document !== 'undefined';\n\n// Input types for which onchange should not be converted to oninput.\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\nconst onChangeInputType = type =>\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\n\t\t? /fil|che|rad/\n\t\t: /fil|che|ra/\n\t).test(type);\n\n// Some libraries like `react-virtualized` explicitly check for this.\nComponent.prototype.isReactComponent = {};\n\n// `UNSAFE_*` lifecycle hooks\n// Preact only ever invokes the unprefixed methods.\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\n// See https://github.com/preactjs/preact/issues/1941\n[\n\t'componentWillMount',\n\t'componentWillReceiveProps',\n\t'componentWillUpdate'\n].forEach(key => {\n\tObject.defineProperty(Component.prototype, key, {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn this['UNSAFE_' + key];\n\t\t},\n\t\tset(v) {\n\t\t\tObject.defineProperty(this, key, {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true,\n\t\t\t\tvalue: v\n\t\t\t});\n\t\t}\n\t});\n});\n\n/**\n * Proxy render() since React returns a Component reference.\n * @param {import('./internal').VNode} vnode VNode tree to render\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\n * @param {() => void} [callback] Optional callback that will be called after rendering\n * @returns {import('./internal').Component | null} The root component reference or null\n */\nexport function render(vnode, parent, callback) {\n\t// React destroys any existing DOM nodes, see #1727\n\t// ...but only on the first render, see #1828\n\tif (parent._children == null) {\n\t\tparent.textContent = '';\n\t}\n\n\tpreactRender(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nexport function hydrate(vnode, parent, callback) {\n\tpreactHydrate(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nlet oldEventHook = options.event;\noptions.event = e => {\n\tif (oldEventHook) e = oldEventHook(e);\n\n\te.persist = empty;\n\te.isPropagationStopped = isPropagationStopped;\n\te.isDefaultPrevented = isDefaultPrevented;\n\treturn (e.nativeEvent = e);\n};\n\nfunction empty() {}\n\nfunction isPropagationStopped() {\n\treturn this.cancelBubble;\n}\n\nfunction isDefaultPrevented() {\n\treturn this.defaultPrevented;\n}\n\nconst classNameDescriptorNonEnumberable = {\n\tenumerable: false,\n\tconfigurable: true,\n\tget() {\n\t\treturn this.class;\n\t}\n};\n\nfunction handleDomVNode(vnode) {\n\tlet props = vnode.props,\n\t\ttype = vnode.type,\n\t\tnormalizedProps = {};\n\n\tlet isNonDashedType = type.indexOf('-') === -1;\n\tfor (let i in props) {\n\t\tlet value = props[i];\n\n\t\tif (\n\t\t\t(i === 'value' && 'defaultValue' in props && value == null) ||\n\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\n\t\t\t(IS_DOM && i === 'children' && type === 'noscript') ||\n\t\t\ti === 'class' ||\n\t\t\ti === 'className'\n\t\t) {\n\t\t\t// Skip applying value if it is null/undefined and we already set\n\t\t\t// a default value\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet lowerCased = i.toLowerCase();\n\t\tif (i === 'defaultValue' && 'value' in props && props.value == null) {\n\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\n\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\n\t\t\ti = 'value';\n\t\t} else if (i === 'download' && value === true) {\n\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\n\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\n\t\t\t// converts it to an empty string instead, otherwise the attribute\n\t\t\t// value will be used as the file name and the file will be called\n\t\t\t// \"true\" upon downloading it.\n\t\t\tvalue = '';\n\t\t} else if (lowerCased === 'translate' && value === 'no') {\n\t\t\tvalue = false;\n\t\t} else if (lowerCased[0] === 'o' && lowerCased[1] === 'n') {\n\t\t\tif (lowerCased === 'ondoubleclick') {\n\t\t\t\ti = 'ondblclick';\n\t\t\t} else if (\n\t\t\t\tlowerCased === 'onchange' &&\n\t\t\t\t(type === 'input' || type === 'textarea') &&\n\t\t\t\t!onChangeInputType(props.type)\n\t\t\t) {\n\t\t\t\tlowerCased = i = 'oninput';\n\t\t\t} else if (lowerCased === 'onfocus') {\n\t\t\t\ti = 'onfocusin';\n\t\t\t} else if (lowerCased === 'onblur') {\n\t\t\t\ti = 'onfocusout';\n\t\t\t} else if (ON_ANI.test(i)) {\n\t\t\t\ti = lowerCased;\n\t\t\t}\n\t\t} else if (isNonDashedType && CAMEL_PROPS.test(i)) {\n\t\t\ti = i.replace(CAMEL_REPLACE, '-$&').toLowerCase();\n\t\t} else if (value === null) {\n\t\t\tvalue = undefined;\n\t\t}\n\n\t\t// Add support for onInput and onChange, see #3561\n\t\t// if we have an oninput prop already change it to oninputCapture\n\t\tif (lowerCased === 'oninput') {\n\t\t\ti = lowerCased;\n\t\t\tif (normalizedProps[i]) {\n\t\t\t\ti = 'oninputCapture';\n\t\t\t}\n\t\t}\n\n\t\tnormalizedProps[i] = value;\n\t}\n\n\t// Add support for array select values: <select multiple value={[]} />\n\tif (\n\t\ttype == 'select' &&\n\t\tnormalizedProps.multiple &&\n\t\tArray.isArray(normalizedProps.value)\n\t) {\n\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\n\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\tchild.props.selected =\n\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\n\t\t});\n\t}\n\n\t// Adding support for defaultValue in select tag\n\tif (type == 'select' && normalizedProps.defaultValue != null) {\n\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\tif (normalizedProps.multiple) {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\n\t\t\t} else {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\n\t\t\t}\n\t\t});\n\t}\n\n\tif (props.class && !props.className) {\n\t\tnormalizedProps.class = props.class;\n\t\tObject.defineProperty(\n\t\t\tnormalizedProps,\n\t\t\t'className',\n\t\t\tclassNameDescriptorNonEnumberable\n\t\t);\n\t} else if (props.className && !props.class) {\n\t\tnormalizedProps.class = normalizedProps.className = props.className;\n\t} else if (props.class && props.className) {\n\t\tnormalizedProps.class = normalizedProps.className = props.className;\n\t}\n\n\tvnode.props = normalizedProps;\n}\n\nlet oldVNodeHook = options.vnode;\noptions.vnode = vnode => {\n\t// only normalize props on Element nodes\n\tif (typeof vnode.type === 'string') {\n\t\thandleDomVNode(vnode);\n\t}\n\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\n\n\tif (oldVNodeHook) oldVNodeHook(vnode);\n};\n\n// Only needed for react-relay\nlet currentComponent;\nconst oldBeforeRender = options._render;\noptions._render = function (vnode) {\n\tif (oldBeforeRender) {\n\t\toldBeforeRender(vnode);\n\t}\n\tcurrentComponent = vnode._component;\n};\n\nconst oldDiffed = options.diffed;\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = function (vnode) {\n\tif (oldDiffed) {\n\t\toldDiffed(vnode);\n\t}\n\n\tconst props = vnode.props;\n\tconst dom = vnode._dom;\n\n\tif (\n\t\tdom != null &&\n\t\tvnode.type === 'textarea' &&\n\t\t'value' in props &&\n\t\tprops.value !== dom.value\n\t) {\n\t\tdom.value = props.value == null ? '' : props.value;\n\t}\n\n\tcurrentComponent = null;\n};\n\n// This is a very very private internal function for React it\n// is used to sort-of do runtime dependency injection.\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\n\tReactCurrentDispatcher: {\n\t\tcurrent: {\n\t\t\treadContext(context) {\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\n\t\t\t},\n\t\t\tuseCallback,\n\t\t\tuseContext,\n\t\t\tuseDebugValue,\n\t\t\tuseDeferredValue,\n\t\t\tuseEffect,\n\t\t\tuseId,\n\t\t\tuseImperativeHandle,\n\t\t\tuseInsertionEffect,\n\t\t\tuseLayoutEffect,\n\t\t\tuseMemo,\n\t\t\t// useMutableSource, // experimental-only and replaced by uSES, likely not worth supporting\n\t\t\tuseReducer,\n\t\t\tuseRef,\n\t\t\tuseState,\n\t\t\tuseSyncExternalStore,\n\t\t\tuseTransition\n\t\t}\n\t}\n};\n", "import {\n\tcreateElement,\n\trender as preactRender,\n\tcloneElement as preactCloneElement,\n\tcreateRef,\n\tComponent,\n\tcreateContext,\n\tFragment\n} from 'preact';\nimport {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue\n} from 'preact/hooks';\nimport { PureComponent } from './PureComponent';\nimport { memo } from './memo';\nimport { forwardRef } from './forwardRef';\nimport { Children } from './Children';\nimport { Suspense, lazy } from './suspense';\nimport { SuspenseList } from './suspense-list';\nimport { createPortal } from './portals';\nimport { is } from './util';\nimport {\n\thydrate,\n\trender,\n\tREACT_ELEMENT_TYPE,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n} from './render';\n\nconst version = '18.3.1'; // trick libraries to think we are react\n\n/**\n * Legacy version of createElement.\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\n */\nfunction createFactory(type) {\n\treturn createElement.bind(null, type);\n}\n\n/**\n * Check if the passed element is a valid (p)react node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isValidElement(element) {\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\n}\n\n/**\n * Check if the passed element is a Fragment node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isFragment(element) {\n\treturn isValidElement(element) && element.type === Fragment;\n}\n\n/**\n * Check if the passed element is a Memo node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isMemo(element) {\n\treturn (\n\t\t!!element &&\n\t\t!!element.displayName &&\n\t\t(typeof element.displayName === 'string' ||\n\t\t\telement.displayName instanceof String) &&\n\t\telement.displayName.startsWith('Memo(')\n\t);\n}\n\n/**\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\n * all vnode normalizations.\n * @param {import('./internal').VNode} element The vnode to clone\n * @param {object} props Props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\n */\nfunction cloneElement(element) {\n\tif (!isValidElement(element)) return element;\n\treturn preactCloneElement.apply(null, arguments);\n}\n\n/**\n * Remove a component tree from the DOM, including state and event handlers.\n * @param {import('./internal').PreactElement} container\n * @returns {boolean}\n */\nfunction unmountComponentAtNode(container) {\n\tif (container._children) {\n\t\tpreactRender(null, container);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\n/**\n * Get the matching DOM node for a component\n * @param {import('./internal').Component} component\n * @returns {import('./internal').PreactElement | null}\n */\nfunction findDOMNode(component) {\n\treturn (\n\t\t(component &&\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\n\t\tnull\n\t);\n}\n\n/**\n * Deprecated way to control batched rendering inside the reconciler, but we\n * already schedule in batches inside our rendering code\n * @template Arg\n * @param {(arg: Arg) => void} callback function that triggers the updated\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n */\n// eslint-disable-next-line camelcase\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\n\n/**\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\n * implmented here as a no-op.\n * @template Arg\n * @template Result\n * @param {(arg: Arg) => Result} callback function that runs before the flush\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n * @returns\n */\nconst flushSync = (callback, arg) => callback(arg);\n\n/**\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\n * that just renders its children without imposing any restrictions.\n */\nconst StrictMode = Fragment;\n\nexport function startTransition(cb) {\n\tcb();\n}\n\nexport function useDeferredValue(val) {\n\treturn val;\n}\n\nexport function useTransition() {\n\treturn [false, startTransition];\n}\n\n// TODO: in theory this should be done after a VNode is diffed as we want to insert\n// styles/... before it attaches\nexport const useInsertionEffect = useLayoutEffect;\n\n// compat to react-is\nexport const isElement = isValidElement;\n\n/**\n * This is taken from https://github.com/facebook/react/blob/main/packages/use-sync-external-store/src/useSyncExternalStoreShimClient.js#L84\n * on a high level this cuts out the warnings, ... and attempts a smaller implementation\n * @typedef {{ _value: any; _getSnapshot: () => any }} Store\n */\nexport function useSyncExternalStore(subscribe, getSnapshot) {\n\tconst value = getSnapshot();\n\n\t/**\n\t * @typedef {{ _instance: Store }} StoreRef\n\t * @type {[StoreRef, (store: StoreRef) => void]}\n\t */\n\tconst [{ _instance }, forceUpdate] = useState({\n\t\t_instance: { _value: value, _getSnapshot: getSnapshot }\n\t});\n\n\tuseLayoutEffect(() => {\n\t\t_instance._value = value;\n\t\t_instance._getSnapshot = getSnapshot;\n\n\t\tif (didSnapshotChange(_instance)) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\t}, [subscribe, value, getSnapshot]);\n\n\tuseEffect(() => {\n\t\tif (didSnapshotChange(_instance)) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\n\t\treturn subscribe(() => {\n\t\t\tif (didSnapshotChange(_instance)) {\n\t\t\t\tforceUpdate({ _instance });\n\t\t\t}\n\t\t});\n\t}, [subscribe]);\n\n\treturn value;\n}\n\n/** @type {(inst: Store) => boolean} */\nfunction didSnapshotChange(inst) {\n\tconst latestGetSnapshot = inst._getSnapshot;\n\tconst prevValue = inst._value;\n\ttry {\n\t\tconst nextValue = latestGetSnapshot();\n\t\treturn !is(prevValue, nextValue);\n\t} catch (error) {\n\t\treturn true;\n\t}\n}\n\nexport * from 'preact/hooks';\nexport {\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tisFragment,\n\tisMemo,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\t// eslint-disable-next-line camelcase\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n\n// React copies the named exports to the default one.\nexport default {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseInsertionEffect,\n\tuseTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tstartTransition,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tisElement,\n\tisFragment,\n\tisMemo,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n"], "names": ["shallow<PERSON>iffers", "a", "b", "i", "PureComponent", "p", "c", "this", "props", "context", "memo", "comparer", "shouldUpdate", "nextProps", "ref", "updateRef", "call", "current", "Memoed", "shouldComponentUpdate", "createElement", "displayName", "name", "prototype", "isReactComponent", "__f", "Component", "isPureReactComponent", "state", "oldDiffHook", "options", "__b", "vnode", "type", "REACT_FORWARD_SYMBOL", "Symbol", "for", "forwardRef", "fn", "Forwarded", "result", "$$typeof", "render", "mapFn", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "Children", "for<PERSON>ach", "count", "length", "only", "normalized", "toArray", "oldCatchError", "__e", "error", "newVNode", "oldVNode", "errorInfo", "then", "component", "__", "__c", "__k", "oldUnmount", "unmount", "detachedClone", "detachedParent", "parentDom", "__H", "effect", "obj", "assign", "__P", "child", "removeOriginal", "originalParent", "__v", "append<PERSON><PERSON><PERSON>", "Suspense", "__u", "_suspenders", "suspended", "__a", "lazy", "loader", "prom", "Lazy", "exports", "default", "e", "SuspenseList", "_next", "_map", "__R", "promise", "suspendingVNode", "suspendingComponent", "push", "resolve", "resolved", "onResolved", "onSuspensionComplete", "suspendedVNode", "__O", "setState", "pop", "forceUpdate", "componentWillUnmount", "document", "detachedComponent", "fallback", "Fragment", "list", "node", "delete", "revealOrder", "size", "ContextProvider", "getChildContext", "Portal", "_this", "container", "_container", "_temp", "nodeType", "parentNode", "childNodes", "contains", "insertBefore", "before", "<PERSON><PERSON><PERSON><PERSON>", "splice", "indexOf", "createPortal", "el", "containerInfo", "delegated", "get", "unsuspend", "wrappedUnsuspend", "Map", "reverse", "set", "componentDidUpdate", "componentDidMount", "REACT_ELEMENT_TYPE", "CAMEL_PROPS", "ON_ANI", "CAMEL_REPLACE", "IS_DOM", "onChangeInputType", "test", "parent", "callback", "textContent", "preactRender", "hydrate", "preactHydrate", "key", "Object", "defineProperty", "configurable", "v", "writable", "value", "oldEventHook", "event", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "persist", "nativeEvent", "currentComponent", "classNameDescriptorNonEnumberable", "enumerable", "class", "oldVNodeHook", "normalizedProps", "isNonDashedType", "lowerCased", "toLowerCase", "replace", "undefined", "multiple", "Array", "isArray", "selected", "defaultValue", "className", "handleDomVNode", "oldBeforeRender", "__r", "oldDiffed", "diffed", "dom", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readContext", "__n", "useCallback", "useContext", "useDebugValue", "useDeferredValue", "useEffect", "useId", "useImperativeHandle", "useInsertionEffect", "useLayoutEffect", "useMemo", "useReducer", "useRef", "useState", "useSyncExternalStore", "useTransition", "version", "createFactory", "bind", "isValidElement", "element", "isFragment", "isMemo", "String", "startsWith", "cloneElement", "preactCloneElement", "apply", "arguments", "unmountComponentAtNode", "findDOMNode", "base", "unstable_batchedUpdates", "arg", "flushSync", "StrictMode", "startTransition", "cb", "val", "isElement", "subscribe", "getSnapshot", "_useState", "_instance", "_getSnapshot", "didSnapshotChange", "inst", "x", "y", "latestGetSnapshot", "prevValue", "nextValue", "index", "createContext", "createRef"], "mappings": "oeAkBO,SAASA,EAAeC,EAAGC,GACjC,IAAK,IAAIC,KAAKF,EAAG,GAAU,aAANE,KAAsBA,KAAKD,GAAI,OAAW,EAC/D,IAAK,IAAIC,KAAKD,EAAG,GAAU,aAANC,GAAoBF,EAAEE,KAAOD,EAAEC,GAAI,OAAW,EACnE,OAAO,CACR,UChBgBC,EAAcC,EAAGC,GAChCC,KAAKC,MAAQH,EACbE,KAAKE,QAAUH,CAChB,CCCgB,SAAAI,EAAKJ,EAAGK,GACvB,SAASC,EAAaC,GACrB,IAAIC,EAAMP,KAAKC,MAAMM,IACjBC,EAAYD,GAAOD,EAAUC,IAKjC,OAJKC,GAAaD,IACjBA,EAAIE,KAAOF,EAAI,MAASA,EAAIG,QAAU,MAGlCN,GAIGA,EAASJ,KAAKC,MAAOK,KAAeE,EAHpCf,EAAeO,KAAKC,MAAOK,EAIpC,CAEA,SAASK,EAAOV,GAEf,OADAD,KAAKY,sBAAwBP,EACtBQ,EAAcd,EAAGE,EACzB,CAIA,OAHAU,EAAOG,YAAc,SAAWf,EAAEe,aAAef,EAAEgB,MAAQ,IAC3DJ,EAAOK,UAAUC,kBAAmB,EACpCN,EAAMO,KAAc,EACbP,CACR,EDvBAd,EAAcmB,UAAY,IAAIG,GAENC,sBAAuB,EAC/CvB,EAAcmB,UAAUJ,sBAAwB,SAAUX,EAAOoB,GAChE,OAAO5B,EAAeO,KAAKC,MAAOA,IAAUR,EAAeO,KAAKqB,MAAOA,EACxE,EEbA,IAAIC,EAAcC,EAAOC,IACzBD,EAAOC,IAAS,SAAAC,GACXA,EAAMC,MAAQD,EAAMC,KAAIR,KAAeO,EAAMlB,MAChDkB,EAAMxB,MAAMM,IAAMkB,EAAMlB,IACxBkB,EAAMlB,IAAM,MAETe,GAAaA,EAAYG,EAC9B,MAEaE,EACM,oBAAVC,QACPA,OAAOC,KACPD,OAAOC,IAAI,sBACZ,KASM,SAASC,EAAWC,GAC1B,SAASC,EAAU/B,GAClB,KAAM,QAASA,GAAQ,OAAO8B,EAAG9B,EAAO,MAExC,IAAIM,EAAMN,EAAMM,WACTN,EAAMM,IACb,IAAM0B,EAASF,EAAG9B,EAAOM,GAEzB,OADAN,EAAMM,IAAMA,EACL0B,CACR,CAYA,OATAD,EAAUE,SAAWP,EAKrBK,EAAUG,OAASH,EAEnBA,EAAUhB,UAAUC,iBAAmBe,EAASd,KAAc,EAC9Dc,EAAUlB,YAAc,eAAiBiB,EAAGjB,aAAeiB,EAAGhB,MAAQ,IAC/DiB,CACR,CC5CA,IAAMI,EAAQ,SAACC,EAAUN,GACxB,OAAgB,MAAZM,EAAyB,KACtBC,EAAaA,EAAaD,GAAUE,IAAIR,GAChD,EAGaS,EAAW,CACvBD,IAAKH,EACLK,QAASL,EACTM,MAAKA,SAACL,GACL,OAAOA,EAAWC,EAAaD,GAAUM,OAAS,CACnD,EACAC,KAAIA,SAACP,GACJ,IAAMQ,EAAaP,EAAaD,GAChC,GAA0B,IAAtBQ,EAAWF,OAAc,KAAM,gBACnC,OAAOE,EAAW,EACnB,EACAC,QAASR,GCfJS,EAAgBxB,EAAOyB,IAC7BzB,EAAOyB,IAAe,SAAUC,EAAOC,EAAUC,EAAUC,GAC1D,GAAIH,EAAMI,KAKT,IAHA,IAAIC,EACA7B,EAAQyB,EAEJzB,EAAQA,EAAK8B,IACpB,IAAKD,EAAY7B,EAAK+B,MAAgBF,EAASE,IAM9C,OALqB,MAAjBN,EAAQF,MACXE,EAAQF,IAAQG,EAAQH,IACxBE,EAAQO,IAAaN,EAAQM,KAGvBH,EAASE,IAAkBP,EAAOC,GAI5CH,EAAcE,EAAOC,EAAUC,EAAUC,EAC1C,EAEA,IAAMM,EAAanC,EAAQoC,QAmB3B,SAASC,EAAcnC,EAAOoC,EAAgBC,GAyB7C,OAxBIrC,IACCA,EAAK+B,KAAe/B,EAAK+B,IAAAO,MAC5BtC,EAAK+B,IAAAO,IAAAR,GAA0Bd,QAAQ,SAAAuB,GACR,mBAAnBA,EAAMR,KAAyBQ,EAAMR,KACjD,GAEA/B,EAAK+B,IAAAO,IAAsB,MAIJ,OADxBtC,EL/Cc,SAAOwC,EAAKhE,GAC3B,IAAK,IAAIL,KAAKK,EAAOgE,EAAIrE,GAAKK,EAAML,GACpC,OAA6BqE,CAC9B,CK4CUC,CAAO,CAAA,EAAIzC,IACV+B,MACJ/B,EAAK+B,IAAAW,MAA2BL,IACnCrC,EAAK+B,IAAAW,IAAyBN,GAE/BpC,EAAK+B,IAAc,MAGpB/B,EAAKgC,IACJhC,EAAKgC,KACLhC,EAAKgC,IAAWlB,IAAI,SAAA6B,GAAK,OACxBR,EAAcQ,EAAOP,EAAgBC,EAAU,IAI3CrC,CACR,CAEA,SAAS4C,EAAe5C,EAAOoC,EAAgBS,GAoB9C,OAnBI7C,GAAS6C,IACZ7C,EAAK8C,IAAa,KAClB9C,EAAKgC,IACJhC,EAAKgC,KACLhC,EAAKgC,IAAWlB,IAAI,SAAA6B,GAAK,OACxBC,EAAeD,EAAOP,EAAgBS,EAAe,GAGnD7C,EAAK+B,KACJ/B,EAAK+B,IAAAW,MAA2BN,IAC/BpC,EAAKuB,KACRsB,EAAeE,YAAY/C,EAAKuB,KAEjCvB,EAAK+B,IAAAR,KAAqB,EAC1BvB,EAAK+B,IAAAW,IAAyBG,IAK1B7C,CACR,CAGgB,SAAAgD,IAEfzE,KAAI0E,IAA2B,EAC/B1E,KAAK2E,EAAc,KACnB3E,KAAIwB,IAAuB,IAC5B,CAqIgB,SAAAoD,EAAUnD,GAEzB,IAAI6B,EAAY7B,EAAK8B,GAAAC,IACrB,OAAOF,GAAaA,EAASuB,KAAevB,EAASuB,IAAYpD,EAClE,CAEO,SAASqD,EAAKC,GACpB,IAAIC,EACA1B,EACAL,EAEJ,SAASgC,EAAKhF,GAab,GAZK+E,IACJA,EAAOD,KACF1B,KACJ,SAAA6B,GACC5B,EAAY4B,EAAQC,SAAWD,CAChC,EACA,SAAAE,GACCnC,EAAQmC,CACT,GAIEnC,EACH,MAAMA,EAGP,IAAKK,EACJ,MAAM0B,EAGP,OAAOnE,EAAcyC,EAAWrD,EACjC,CAIA,OAFAgF,EAAKnE,YAAc,OACnBmE,EAAI/D,KAAc,EACX+D,CACR,UCvQgBI,IACfrF,KAAKsF,EAAQ,KACbtF,KAAKuF,EAAO,IACb,CDcAhE,EAAQoC,QAAU,SAAUlC,GAE3B,IAAM6B,EAAY7B,EAAK+B,IACnBF,GAAaA,EAASkC,KACzBlC,EAASkC,MAONlC,GEpCuB,GFoCV7B,EAAKiD,MACrBjD,EAAMC,KAAO,MAGVgC,GAAYA,EAAWjC,EAC5B,GAgEAgD,EAASzD,UAAY,IAAIG,GAOPqC,IAAoB,SAAUiC,EAASC,GACxD,IAAMC,EAAsBD,EAAelC,IAGrCzD,EAAIC,KAEW,MAAjBD,EAAE4E,IACL5E,EAAE4E,EAAc,IAEjB5E,EAAE4E,EAAYiB,KAAKD,GAEnB,IAAME,EAAUjB,EAAU7E,EAACwE,KAEvBuB,GAAW,EACTC,EAAa,WACdD,IAEJA,GAAW,EACXH,EAAmBH,IAAc,KAE7BK,EACHA,EAAQG,GAERA,IAEF,EAEAL,EAAmBH,IAAcO,EAEjC,IAAMC,EAAuB,WAC5B,MAAOjG,EAAC2E,IAA0B,CAGjC,GAAI3E,EAAEsB,MAAKwD,IAAa,CACvB,IAAMoB,EAAiBlG,EAAEsB,MAAKwD,IAC9B9E,EAACwE,IAAAd,IAAkB,GAAKY,EACvB4B,EACAA,EAAczC,IAAAW,IACd8B,EAAczC,IAAA0C,IAEhB,CAIA,IAAItB,EACJ,IAHA7E,EAAEoG,SAAS,CAAEtB,IAAa9E,EAACyB,IAAuB,OAG1CoD,EAAY7E,EAAE4E,EAAYyB,OACjCxB,EAAUyB,aAEZ,CACD,EAQEtG,EAAC2E,OEzKwB,GF0KxBgB,EAAehB,KAEjB3E,EAAEoG,SAAS,CAAEtB,IAAa9E,EAACyB,IAAuBzB,EAACwE,IAAAd,IAAkB,KAEtEgC,EAAQpC,KAAK0C,EAAYA,EAC1B,EAEAtB,EAASzD,UAAUsF,qBAAuB,WACzCtG,KAAK2E,EAAc,EACpB,EAOAF,EAASzD,UAAUmB,OAAS,SAAUlC,EAAOoB,GAC5C,GAAIrB,KAAIwB,IAAsB,CAI7B,GAAIxB,KAAIuE,IAAAd,IAAmB,CAC1B,IAAMI,EAAiB0C,SAAS1F,cAAc,OACxC2F,EAAoBxG,KAAIuE,IAAAd,IAAkB,GAAED,IAClDxD,KAAIuE,IAAAd,IAAkB,GAAKG,EAC1B5D,KAAIwB,IACJqC,EACC2C,EAAiBN,IAAsBM,EAAiBrC,IAE3D,CAEAnE,KAAIwB,IAAuB,IAC5B,CAIA,IAAMiF,EACLpF,EAAKwD,KAAehE,EAAc6F,EAAU,KAAMzG,EAAMwG,UAGzD,OAFIA,IAAUA,EAAQ/B,MAAW,IAE1B,CACN7D,EAAc6F,EAAU,KAAMrF,EAAKwD,IAAc,KAAO5E,EAAMoC,UAC9DoE,EAEF,ECrMA,IAAMZ,EAAU,SAACc,EAAMvC,EAAOwC,GAc7B,KAbMA,EAdgB,KAcSA,EAfR,IAqBtBD,EAAKpB,EAAKsB,OAAOzC,GAQhBuC,EAAK1G,MAAM6G,cACmB,MAA9BH,EAAK1G,MAAM6G,YAAY,KAAcH,EAAKpB,EAAKwB,MASjD,IADAH,EAAOD,EAAKrB,EACLsB,GAAM,CACZ,KAAOA,EAAKjE,OAAS,GACpBiE,EAAKR,KAALQ,GAED,GAAIA,EA1CiB,GA0CMA,EA3CL,GA4CrB,MAEDD,EAAKrB,EAAQsB,EAAOA,EA5CJ,EA6CjB,CACD,EE/CA,SAASI,EAAgB/G,GAExB,OADAD,KAAKiH,gBAAkB,WAAM,OAAAhH,EAAMC,OAAO,EACnCD,EAAMoC,QACd,CASA,SAAS6E,EAAOjH,GACf,IAAMkH,EAAQnH,KACVoH,EAAYnH,EAAMoH,EAEtBF,EAAMb,qBAAuB,WAC5BnE,EAAO,KAAMgF,EAAMG,GACnBH,EAAMG,EAAQ,KACdH,EAAME,EAAa,IACpB,EAIIF,EAAME,GAAcF,EAAME,IAAeD,GAC5CD,EAAMb,uBAGFa,EAAMG,IACVH,EAAME,EAAaD,EAGnBD,EAAMG,EAAQ,CACbC,SAAU,EACVC,WAAYJ,EACZK,WAAY,GACZC,SAAU,WAAM,OAAA,CAAI,EACpBlD,YAAWA,SAACJ,GACXpE,KAAKyH,WAAW7B,KAAKxB,GACrB+C,EAAME,EAAW7C,YAAYJ,EAC9B,EACAuD,aAAYA,SAACvD,EAAOwD,GACnB5H,KAAKyH,WAAW7B,KAAKxB,GACrB+C,EAAME,EAAW7C,YAAYJ,EAC9B,EACAyD,YAAWA,SAACzD,GACXpE,KAAKyH,WAAWK,OAAO9H,KAAKyH,WAAWM,QAAQ3D,KAAW,EAAG,GAC7D+C,EAAME,EAAWQ,YAAYzD,EAC9B,IAKFjC,EACCtB,EAAcmG,EAAiB,CAAE9G,QAASiH,EAAMjH,SAAWD,EAAKsE,KAChE4C,EAAMG,EAER,CAOgB,SAAAU,EAAavG,EAAO2F,GACnC,IAAMa,EAAKpH,EAAcqG,EAAQ,CAAE3C,IAAQ9C,EAAO4F,EAAYD,IAE9D,OADAa,EAAGC,cAAgBd,EACZa,CACR,EFhBA5C,EAAarE,UAAY,IAAIG,GAEP0D,IAAc,SAAUT,GAC7C,IAAMuC,EAAO3G,KACPmI,EAAYvD,EAAU+B,EAAIpC,KAE5BqC,EAAOD,EAAKpB,EAAK6C,IAAIhE,GAGzB,OAFAwC,EA5DuB,KA8DhB,SAAAyB,GACN,IAAMC,EAAmB,WACnB3B,EAAK1G,MAAM6G,aAKfF,EAAKhB,KAAKyC,GACVxC,EAAQc,EAAMvC,EAAOwC,IAHrByB,GAKF,EACIF,EACHA,EAAUG,GAEVA,GAEF,CACD,EAEAjD,EAAarE,UAAUmB,OAAS,SAAUlC,GACzCD,KAAKsF,EAAQ,KACbtF,KAAKuF,EAAO,IAAIgD,IAEhB,IAAMlG,EAAWC,EAAarC,EAAMoC,UAChCpC,EAAM6G,aAAwC,MAAzB7G,EAAM6G,YAAY,IAI1CzE,EAASmG,UAIV,IAAK,IAAI5I,EAAIyC,EAASM,OAAQ/C,KAY7BI,KAAKuF,EAAKkD,IAAIpG,EAASzC,GAAKI,KAAKsF,EAAQ,CAAC,EAAG,EAAGtF,KAAKsF,IAEtD,OAAOrF,EAAMoC,QACd,EAEAgD,EAAarE,UAAU0H,mBACtBrD,EAAarE,UAAU2H,kBAAoB,eAAYxB,EAAAnH,KAOtDA,KAAKuF,EAAK9C,QAAQ,SAACmE,EAAMxC,GACxByB,EAAQsB,EAAM/C,EAAOwC,EACtB,EACD,EGnGY,IAAAgC,EACM,oBAAVhH,QAAyBA,OAAOC,KAAOD,OAAOC,IAAI,kBAC1D,MAEKgH,EACL,8RACKC,EAAS,mCACTC,EAAgB,YAChBC,EAA6B,oBAAbzC,SAKhB0C,EAAoB,SAAAvH,GAAI,OACX,oBAAVE,QAA4C,iBAAZA,SACrC,cACA,cACDsH,KAAKxH,EAAK,EAuCN,SAASS,EAAOV,EAAO0H,EAAQC,GAUrC,OAPwB,MAApBD,EAAM1F,MACT0F,EAAOE,YAAc,IAGtBC,EAAa7H,EAAO0H,GACG,mBAAZC,GAAwBA,IAE5B3H,EAAQA,EAAK+B,IAAc,IACnC,CAEO,SAAS+F,EAAQ9H,EAAO0H,EAAQC,GAItC,OAHAI,EAAc/H,EAAO0H,GACE,mBAAZC,GAAwBA,IAE5B3H,EAAQA,EAAK+B,IAAc,IACnC,CAtDArC,EAAUH,UAAUC,iBAAmB,CAAA,EASvC,CACC,qBACA,4BACA,uBACCwB,QAAQ,SAAAgH,GACTC,OAAOC,eAAexI,EAAUH,UAAWyI,EAAK,CAC/CG,cAAc,EACdxB,IAAGA,WACF,OAAWpI,KAAC,UAAYyJ,EACzB,EACAhB,IAAGA,SAACoB,GACHH,OAAOC,eAAe3J,KAAMyJ,EAAK,CAChCG,cAAc,EACdE,UAAU,EACVC,MAAOF,GAET,GAEF,GA6BA,IAAIG,EAAezI,EAAQ0I,MAU3B,SAASC,IAAQ,CAEjB,SAASC,IACR,OAAOnK,KAAKoK,YACb,CAEA,SAASC,IACR,YAAYC,gBACb,CAjBA/I,EAAQ0I,MAAQ,SAAA7E,GAMf,OALI4E,IAAc5E,EAAI4E,EAAa5E,IAEnCA,EAAEmF,QAAUL,EACZ9E,EAAE+E,qBAAuBA,EACzB/E,EAAEiF,mBAAqBA,EACfjF,EAAEoF,YAAcpF,CACzB,EAYA,IAoIIqF,EApIEC,GAAoC,CACzCC,YAAY,EACZf,cAAc,EACdxB,IAAG,WACF,OAAOpI,KAAK4K,KACb,GAkHGC,GAAetJ,EAAQE,MAC3BF,EAAQE,MAAQ,SAAAA,GAEW,iBAAfA,EAAMC,MAlHlB,SAAwBD,GACvB,IAAIxB,EAAQwB,EAAMxB,MACjByB,EAAOD,EAAMC,KACboJ,EAAkB,CAAE,EAEjBC,GAAyC,IAAvBrJ,EAAKqG,QAAQ,KACnC,IAAK,IAAInI,KAAKK,EAAO,CACpB,IAAI8J,EAAQ9J,EAAML,GAElB,KACQ,UAANA,GAAiB,iBAAkBK,GAAkB,MAAT8J,GAE5Cf,GAAgB,aAANpJ,GAA6B,aAAT8B,GACzB,UAAN9B,GACM,cAANA,GALD,CAYA,IAAIoL,EAAapL,EAAEqL,cACT,iBAANrL,GAAwB,UAAWK,GAAwB,MAAfA,EAAM8J,MAGrDnK,EAAI,QACY,aAANA,IAA8B,IAAVmK,EAM9BA,EAAQ,GACiB,cAAfiB,GAAwC,OAAVjB,EACxCA,GAAQ,EACoB,MAAlBiB,EAAW,IAAgC,MAAlBA,EAAW,GAC3B,kBAAfA,EACHpL,EAAI,aAEW,aAAfoL,GACU,UAATtJ,GAA6B,aAATA,GACpBuH,EAAkBhJ,EAAMyB,MAGA,YAAfsJ,EACVpL,EAAI,YACqB,WAAfoL,EACVpL,EAAI,aACMkJ,EAAOI,KAAKtJ,KACtBA,EAAIoL,GANJA,EAAapL,EAAI,UAQRmL,GAAmBlC,EAAYK,KAAKtJ,GAC9CA,EAAIA,EAAEsL,QAAQnC,EAAe,OAAOkC,cAChB,OAAVlB,IACVA,OAAQoB,GAKU,YAAfH,GAECF,EADJlL,EAAIoL,KAEHpL,EAAI,kBAINkL,EAAgBlL,GAAKmK,CA/CrB,CAgDD,CAIS,UAARrI,GACAoJ,EAAgBM,UAChBC,MAAMC,QAAQR,EAAgBf,SAG9Be,EAAgBf,MAAQzH,EAAarC,EAAMoC,UAAUI,QAAQ,SAAA2B,GAC5DA,EAAMnE,MAAMsL,UAC0C,GAArDT,EAAgBf,MAAMhC,QAAQ3D,EAAMnE,MAAM8J,MAC5C,IAIW,UAARrI,GAAoD,MAAhCoJ,EAAgBU,eACvCV,EAAgBf,MAAQzH,EAAarC,EAAMoC,UAAUI,QAAQ,SAAA2B,GAE3DA,EAAMnE,MAAMsL,SADTT,EAAgBM,UAE0C,GAA5DN,EAAgBU,aAAazD,QAAQ3D,EAAMnE,MAAM8J,OAGjDe,EAAgBU,cAAgBpH,EAAMnE,MAAM8J,KAE/C,IAGG9J,EAAM2K,QAAU3K,EAAMwL,WACzBX,EAAgBF,MAAQ3K,EAAM2K,MAC9BlB,OAAOC,eACNmB,EACA,YACAJ,MAESzK,EAAMwL,YAAcxL,EAAM2K,OAE1B3K,EAAM2K,OAAS3K,EAAMwL,aAD/BX,EAAgBF,MAAQE,EAAgBW,UAAYxL,EAAMwL,WAK3DhK,EAAMxB,MAAQ6K,CACf,CAMEY,CAAejK,GAGhBA,EAAMS,SAAW0G,EAEbiC,IAAcA,GAAapJ,EAChC,EAIA,IAAMkK,GAAkBpK,EAAOqK,IAC/BrK,EAAOqK,IAAW,SAAUnK,GACvBkK,IACHA,GAAgBlK,GAEjBgJ,EAAmBhJ,EAAK+B,GACzB,EAEA,IAAMqI,GAAYtK,EAAQuK,OAE1BvK,EAAQuK,OAAS,SAAUrK,GACtBoK,IACHA,GAAUpK,GAGX,IAAMxB,EAAQwB,EAAMxB,MACd8L,EAAMtK,EAAKuB,IAGT,MAAP+I,GACe,aAAftK,EAAMC,MACN,UAAWzB,GACXA,EAAM8J,QAAUgC,EAAIhC,QAEpBgC,EAAIhC,MAAuB,MAAf9J,EAAM8J,MAAgB,GAAK9J,EAAM8J,OAG9CU,EAAmB,IACpB,EAIa,IAAAuB,GAAqD,CACjEC,uBAAwB,CACvBvL,QAAS,CACRwL,YAAWA,SAAChM,GACX,OAAOuK,EAAgB0B,IAAgBjM,EAAOsD,KAAMvD,MAAM8J,KAC3D,EACAqC,YAAAA,EACAC,WAAAA,EACAC,cAAAA,EACAC,iBAAAA,GACAC,UAAAA,EACAC,MAAAA,EACAC,oBAAAA,EACAC,mBAAAA,GACAC,gBAAAA,EACAC,QAAAA,EAEAC,WAAAA,EACAC,OAAAA,EACAC,SAAAA,EACAC,qBAAAA,GACAC,cAAAA,MChRGC,GAAU,SAMhB,SAASC,GAAc1L,GACtB,OAAOb,EAAcwM,KAAK,KAAM3L,EACjC,CAOA,SAAS4L,GAAeC,GACvB,QAASA,GAAWA,EAAQrL,WAAa0G,CAC1C,CAOA,SAAS4E,GAAWD,GACnB,OAAOD,GAAeC,IAAYA,EAAQ7L,OAASgF,CACpD,CAOA,SAAS+G,GAAOF,GACf,QACGA,KACAA,EAAQzM,cACsB,iBAAxByM,EAAQzM,aACfyM,EAAQzM,uBAAuB4M,SAChCH,EAAQzM,YAAY6M,WAAW,QAEjC,CASA,SAASC,GAAaL,GACrB,OAAKD,GAAeC,GACbM,EAAmBC,MAAM,KAAMC,WADDR,CAEtC,CAOA,SAASS,GAAuB5G,GAC/B,QAAIA,EAAS3D,MACZ6F,EAAa,KAAMlC,MAIrB,CAOA,SAAS6G,GAAY3K,GACpB,OACEA,IACCA,EAAU4K,MAAgC,IAAvB5K,EAAUiE,UAAkBjE,IACjD,IAEF,CAUM,IAAA6K,GAA0B,SAAC/E,EAAUgF,GAAQ,OAAAhF,EAASgF,EAAI,EAW1DC,GAAY,SAACjF,EAAUgF,UAAQhF,EAASgF,EAAI,EAM5CE,GAAa5H,EAEH,SAAA6H,GAAgBC,GAC/BA,GACD,UAEgBjC,GAAiBkC,GAChC,OAAOA,CACR,CAEO,SAASvB,KACf,MAAO,EAAC,EAAOqB,GAChB,CAIa,IAAA5B,GAAqBC,EAGrB8B,GAAYpB,GAOlB,SAASL,GAAqB0B,EAAWC,GAC/C,IAAM7E,EAAQ6E,IAMdC,EAAqC7B,EAAS,CAC7C8B,EAAW,CAAEvL,GAAQwG,EAAOgF,EAAcH,KADlCE,EAASD,EAATC,GAAAA,EAAazI,EAAWwI,EAAA,GAyBjC,OArBAjC,EAAgB,WACfkC,EAASvL,GAAUwG,EACnB+E,EAAUC,EAAeH,EAErBI,GAAkBF,IACrBzI,EAAY,CAAEyI,EAAAA,GAEhB,EAAG,CAACH,EAAW5E,EAAO6E,IAEtBpC,EAAU,WAKT,OAJIwC,GAAkBF,IACrBzI,EAAY,CAAEyI,EAAAA,IAGRH,EAAU,WACZK,GAAkBF,IACrBzI,EAAY,CAAEyI,EAAAA,GAEhB,EACD,EAAG,CAACH,IAEG5E,CACR,CAGA,SAASiF,GAAkBC,GAC1B,IVhLkBC,EAAGC,EUgLfC,EAAoBH,EAAKF,EACzBM,EAAYJ,EAAI1L,GACtB,IACC,IAAM+L,EAAYF,IAClB,SVpLiBF,EUoLNG,MVpLSF,EUoLEG,KVnLG,IAANJ,GAAW,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,EUsLtE,CAFE,MAAOlM,GACR,QACD,CACD,CAmCA,IAAesM,GAAA,CACdvC,SAAAA,EACAP,MAAAA,EACAK,WAAAA,EACAN,UAAAA,EACAI,gBAAAA,EACAD,mBAAAA,GACAO,cAAAA,GACAX,iBAAAA,GACAU,qBAAAA,GACAsB,gBAAAA,GACAxB,OAAAA,EACAL,oBAAAA,EACAG,QAAAA,EACAT,YAAAA,EACAC,WAAAA,EACAC,cAAAA,EACAa,QArOe,SAsOf3K,SAAAA,EACAL,OAAAA,EACAoH,QAAAA,EACAyE,uBAAAA,GACAhG,aAAAA,EACAnH,cAAAA,EACA2O,cAAAA,EACApC,cAAAA,GACAQ,aAAAA,GACA6B,UAAAA,EACA/I,SAAAA,EACA4G,eAAAA,GACAoB,UAAAA,GACAlB,WAAAA,GACAC,OAAAA,GACAQ,YAAAA,GACA9M,UAAAA,EACAtB,cAAAA,EACAM,KAAAA,EACA2B,WAAAA,EACAuM,UAAAA,GACAF,wBAAAA,GACAG,WAAAA,GACA7J,SAAAA,EACAY,aAAAA,EACAP,KAAAA,EACAkH,mDAAAA"}