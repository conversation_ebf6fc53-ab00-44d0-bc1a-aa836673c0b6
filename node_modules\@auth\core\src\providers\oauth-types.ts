
// THIS FILE IS AUTOGENERATED. DO NOT EDIT.
export type OAuthProviderType = 
  | "42-school"
  | "apple"
  | "asgardeo"
  | "atlassian"
  | "auth0"
  | "authentik"
  | "azure-ad-b2c"
  | "azure-ad"
  | "azure-devops"
  | "bankid-no"
  | "battlenet"
  | "beyondidentity"
  | "box"
  | "boxyhq-saml"
  | "bungie"
  | "click-up"
  | "cognito"
  | "coinbase"
  | "concept2"
  | "descope"
  | "discord"
  | "dribbble"
  | "dropbox"
  | "duende-identity-server6"
  | "eventbrite"
  | "eveonline"
  | "facebook"
  | "faceit"
  | "forwardemail"
  | "foursquare"
  | "freshbooks"
  | "fusionauth"
  | "github"
  | "gitlab"
  | "google"
  | "hubspot"
  | "identity-server4"
  | "instagram"
  | "kakao"
  | "keycloak"
  | "kinde"
  | "line"
  | "linkedin"
  | "mailchimp"
  | "mailgun"
  | "mailru"
  | "mastodon"
  | "mattermost"
  | "medium"
  | "microsoft-entra-id"
  | "naver"
  | "netlify"
  | "netsuite"
  | "nextcloud"
  | "nodemailer"
  | "notion"
  | "okta"
  | "onelogin"
  | "ory-hydra"
  | "osso"
  | "osu"
  | "passage"
  | "passkey"
  | "patreon"
  | "ping-id"
  | "pinterest"
  | "pipedrive"
  | "postmark"
  | "reddit"
  | "resend"
  | "roblox"
  | "salesforce"
  | "sendgrid"
  | "simplelogin"
  | "slack"
  | "spotify"
  | "strava"
  | "threads"
  | "tiktok"
  | "todoist"
  | "trakt"
  | "twitch"
  | "twitter"
  | "united-effects"
  | "vipps"
  | "vk"
  | "webauthn"
  | "webex"
  | "wechat"
  | "wikimedia"
  | "wordpress"
  | "workos"
  | "yandex"
  | "zitadel"
  | "zoho"
  | "zoom"