"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/packages/route";
exports.ids = ["app/api/packages/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_packages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/packages/route.ts */ \"(rsc)/./src/app/api/packages/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/packages/route\",\n        pathname: \"/api/packages\",\n        filename: \"route\",\n        bundlePath: \"app/api/packages/route\"\n    },\n    resolvedPagePath: \"D:\\\\CURSOR-AI\\\\ai-project\\\\TintPlatesStdio\\\\src\\\\app\\\\api\\\\packages\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_packages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/packages/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/packages/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/packages/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst DEFAULT_PACKAGES = {\n    pilates: [\n        // Bireysel Paketler\n        {\n            type: \"individual\",\n            hours: 8,\n            maxStudents: 1,\n            name: \"Bireysel 8 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 12,\n            maxStudents: 1,\n            name: \"Bireysel 12 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 16,\n            maxStudents: 1,\n            name: \"Bireysel 16 Saat\"\n        },\n        // Düet Paketler\n        {\n            type: \"duet\",\n            hours: 8,\n            maxStudents: 2,\n            name: \"D\\xfcet 8 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 12,\n            maxStudents: 2,\n            name: \"D\\xfcet 12 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 16,\n            maxStudents: 2,\n            name: \"D\\xfcet 16 Saat\"\n        },\n        // Grup Paketler\n        {\n            type: \"group\",\n            hours: 8,\n            maxStudents: 3,\n            name: \"Grup 8 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 12,\n            maxStudents: 3,\n            name: \"Grup 12 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 16,\n            maxStudents: 3,\n            name: \"Grup 16 Saat\"\n        }\n    ],\n    physiotherapy: [\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Skolyoz Tedavisi\",\n            details: \"Skolyoz değerlendirme ve tedavi programı\"\n        },\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Manuel Terapi\",\n            details: \"Manuel terapi teknikleri ile tedavi\"\n        },\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Pelvik Taban\",\n            details: \"Pelvik taban rehabilitasyonu\"\n        },\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"FTR\",\n            details: \"Fizik tedavi ve rehabilitasyon programı\"\n        }\n    ],\n    clinicalPilates: [\n        // Bireysel Paketler\n        {\n            type: \"individual\",\n            hours: 8,\n            maxStudents: 1,\n            name: \"Klinik Pilates Bireysel 8 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 12,\n            maxStudents: 1,\n            name: \"Klinik Pilates Bireysel 12 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 16,\n            maxStudents: 1,\n            name: \"Klinik Pilates Bireysel 16 Saat\"\n        },\n        // Düet Paketler\n        {\n            type: \"duet\",\n            hours: 8,\n            maxStudents: 2,\n            name: \"Klinik Pilates D\\xfcet 8 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 12,\n            maxStudents: 2,\n            name: \"Klinik Pilates D\\xfcet 12 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 16,\n            maxStudents: 2,\n            name: \"Klinik Pilates D\\xfcet 16 Saat\"\n        },\n        // Grup Paketler\n        {\n            type: \"group\",\n            hours: 8,\n            maxStudents: 3,\n            name: \"Klinik Pilates Grup 8 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 12,\n            maxStudents: 3,\n            name: \"Klinik Pilates Grup 12 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 16,\n            maxStudents: 3,\n            name: \"Klinik Pilates Grup 16 Saat\"\n        }\n    ],\n    zumba: [\n        {\n            type: \"group\",\n            maxStudents: 6,\n            minStudents: 4,\n            name: \"Zumba Grup Dersi\",\n            singleClass: true\n        }\n    ],\n    yoga: [\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Yoga \\xd6zel Ders\"\n        },\n        {\n            type: \"group\",\n            maxStudents: 6,\n            minStudents: 4,\n            name: \"Yoga Grup Dersi\",\n            singleClass: true\n        }\n    ],\n    aerialYoga: [\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Hamak Yoga \\xd6zel Ders\"\n        },\n        {\n            type: \"group\",\n            maxStudents: 6,\n            minStudents: 4,\n            name: \"Hamak Yoga Grup Dersi\",\n            singleClass: true\n        }\n    ]\n};\nasync function POST(request) {\n    try {\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const data = await request.json();\n        if (!data.serviceId || !data.type || !data.name || !data.price) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Gerekli alanlar eksik\"\n            }, {\n                status: 400\n            });\n        }\n        const newPackage = {\n            serviceId: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.serviceId),\n            type: data.type,\n            name: data.name,\n            hours: data.hours,\n            maxStudents: data.maxStudents,\n            minStudents: data.minStudents,\n            price: data.price,\n            details: data.details,\n            isActive: true,\n            createdAt: new Date()\n        };\n        const result = await db.collection(\"packages\").insertOne(newPackage);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Paket başarıyla eklendi\",\n            packageData: {\n                id: result.insertedId,\n                ...newPackage\n            }\n        });\n    } catch (error) {\n        console.error(\"Paket ekleme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const serviceId = searchParams.get(\"serviceId\");\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const query = serviceId ? {\n            serviceId: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(serviceId)\n        } : {};\n        const packages = await db.collection(\"packages\").find(query).toArray();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            packages: packages.map((pkg)=>({\n                    id: pkg._id,\n                    serviceId: pkg.serviceId,\n                    type: pkg.type,\n                    name: pkg.name,\n                    hours: pkg.hours,\n                    maxStudents: pkg.maxStudents,\n                    minStudents: pkg.minStudents,\n                    price: pkg.price,\n                    details: pkg.details,\n                    isActive: pkg.isActive\n                }))\n        });\n    } catch (error) {\n        console.error(\"Paket listesi hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT metodu için\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID parametresi gerekli\"\n            }, {\n                status: 400\n            });\n        }\n        const data = await request.json();\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const result = await db.collection(\"packages\").updateOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: {\n                ...data,\n                updatedAt: new Date()\n            }\n        });\n        if (result.matchedCount === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Paket bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Paket başarıyla g\\xfcncellendi\"\n        });\n    } catch (error) {\n        console.error(\"Paket g\\xfcncelleme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE metodu için\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID parametresi gerekli\"\n            }, {\n                status: 400\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const result = await db.collection(\"packages\").deleteOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        });\n        if (result.deletedCount === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Paket bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Paket başarıyla silindi\"\n        });\n    } catch (error) {\n        console.error(\"Paket silme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/packages/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış.\");\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {};\nlet client;\nlet clientPromise;\nif (true) {\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();