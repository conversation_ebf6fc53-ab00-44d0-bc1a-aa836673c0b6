"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/packages/route";
exports.ids = ["app/api/packages/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_packages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/packages/route.ts */ \"(rsc)/./src/app/api/packages/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/packages/route\",\n        pathname: \"/api/packages\",\n        filename: \"route\",\n        bundlePath: \"app/api/packages/route\"\n    },\n    resolvedPagePath: \"D:\\\\CURSOR-AI\\\\ai-project\\\\TintPlatesStdio\\\\src\\\\app\\\\api\\\\packages\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_packages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/packages/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/packages/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/packages/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst DEFAULT_PACKAGES = {\n    pilates: [\n        // Bireysel Paketler\n        {\n            type: \"individual\",\n            hours: 8,\n            maxStudents: 1,\n            name: \"Bireysel 8 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 12,\n            maxStudents: 1,\n            name: \"Bireysel 12 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 16,\n            maxStudents: 1,\n            name: \"Bireysel 16 Saat\"\n        },\n        // Düet Paketler\n        {\n            type: \"duet\",\n            hours: 8,\n            maxStudents: 2,\n            name: \"D\\xfcet 8 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 12,\n            maxStudents: 2,\n            name: \"D\\xfcet 12 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 16,\n            maxStudents: 2,\n            name: \"D\\xfcet 16 Saat\"\n        },\n        // Grup Paketler\n        {\n            type: \"group\",\n            hours: 8,\n            maxStudents: 3,\n            name: \"Grup 8 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 12,\n            maxStudents: 3,\n            name: \"Grup 12 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 16,\n            maxStudents: 3,\n            name: \"Grup 16 Saat\"\n        }\n    ],\n    physiotherapy: [\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Skolyoz Tedavisi\",\n            details: \"Skolyoz değerlendirme ve tedavi programı\"\n        },\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Manuel Terapi\",\n            details: \"Manuel terapi teknikleri ile tedavi\"\n        },\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Pelvik Taban\",\n            details: \"Pelvik taban rehabilitasyonu\"\n        },\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"FTR\",\n            details: \"Fizik tedavi ve rehabilitasyon programı\"\n        }\n    ],\n    clinicalPilates: [\n        // Bireysel Paketler\n        {\n            type: \"individual\",\n            hours: 8,\n            maxStudents: 1,\n            name: \"Klinik Pilates Bireysel 8 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 12,\n            maxStudents: 1,\n            name: \"Klinik Pilates Bireysel 12 Saat\"\n        },\n        {\n            type: \"individual\",\n            hours: 16,\n            maxStudents: 1,\n            name: \"Klinik Pilates Bireysel 16 Saat\"\n        },\n        // Düet Paketler\n        {\n            type: \"duet\",\n            hours: 8,\n            maxStudents: 2,\n            name: \"Klinik Pilates D\\xfcet 8 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 12,\n            maxStudents: 2,\n            name: \"Klinik Pilates D\\xfcet 12 Saat\"\n        },\n        {\n            type: \"duet\",\n            hours: 16,\n            maxStudents: 2,\n            name: \"Klinik Pilates D\\xfcet 16 Saat\"\n        },\n        // Grup Paketler\n        {\n            type: \"group\",\n            hours: 8,\n            maxStudents: 3,\n            name: \"Klinik Pilates Grup 8 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 12,\n            maxStudents: 3,\n            name: \"Klinik Pilates Grup 12 Saat\"\n        },\n        {\n            type: \"group\",\n            hours: 16,\n            maxStudents: 3,\n            name: \"Klinik Pilates Grup 16 Saat\"\n        }\n    ],\n    zumba: [\n        {\n            type: \"group\",\n            maxStudents: 6,\n            minStudents: 4,\n            name: \"Zumba Grup Dersi\",\n            singleClass: true\n        }\n    ],\n    yoga: [\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Yoga \\xd6zel Ders\"\n        },\n        {\n            type: \"group\",\n            maxStudents: 6,\n            minStudents: 4,\n            name: \"Yoga Grup Dersi\",\n            singleClass: true\n        }\n    ],\n    aerialYoga: [\n        {\n            type: \"individual\",\n            maxStudents: 1,\n            name: \"Hamak Yoga \\xd6zel Ders\"\n        },\n        {\n            type: \"group\",\n            maxStudents: 6,\n            minStudents: 4,\n            name: \"Hamak Yoga Grup Dersi\",\n            singleClass: true\n        }\n    ]\n};\nasync function POST(request) {\n    try {\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const data = await request.json();\n        console.log(\"Gelen paket verisi:\", data);\n        // Gerekli alan kontrolü\n        if (!data.type || !data.name || !data.price) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Paket tipi, adı ve fiyatı gereklidir\"\n            }, {\n                status: 400\n            });\n        }\n        if (!data.serviceType && !data.serviceId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Hizmet tipi veya hizmet ID'si gereklidir\"\n            }, {\n                status: 400\n            });\n        }\n        // serviceId kontrolü ve oluşturma\n        let serviceObjectId = null;\n        if (data.serviceId && data.serviceId !== \"default\") {\n            try {\n                // Geçerli ObjectId kontrolü\n                if (mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId.isValid(data.serviceId)) {\n                    serviceObjectId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.serviceId);\n                }\n            } catch (error) {\n                console.log(\"Ge\\xe7ersiz serviceId:\", data.serviceId);\n            }\n        }\n        // serviceId yoksa hizmet tipine göre bul veya oluştur\n        if (!serviceObjectId && data.serviceType) {\n            try {\n                const service = await db.collection(\"services\").findOne({\n                    type: data.serviceType\n                });\n                if (service) {\n                    serviceObjectId = service._id;\n                } else {\n                    // Hizmet bulunamazsa yeni bir ObjectId oluştur\n                    serviceObjectId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId();\n                }\n            } catch (error) {\n                console.log(\"Hizmet bulunamadı, yeni ObjectId oluşturuluyor\");\n                serviceObjectId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId();\n            }\n        }\n        const newPackage = {\n            serviceId: serviceObjectId,\n            serviceType: data.serviceType || \"pilates\",\n            type: data.type,\n            name: data.name.trim(),\n            hours: Number(data.hours) || 0,\n            maxStudents: Number(data.maxStudents) || 1,\n            minStudents: data.minStudents ? Number(data.minStudents) : undefined,\n            price: Number(data.price),\n            details: data.details?.trim() || \"\",\n            isActive: Boolean(data.isActive),\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        console.log(\"Kaydedilecek paket:\", newPackage);\n        const result = await db.collection(\"packages\").insertOne(newPackage);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Paket başarıyla eklendi\",\n            packageData: {\n                id: result.insertedId.toString(),\n                ...newPackage,\n                serviceId: newPackage.serviceId?.toString(),\n                _id: undefined // MongoDB _id'sini kaldır\n            }\n        });\n    } catch (error) {\n        console.error(\"Paket ekleme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"Paket eklenirken bir hata oluştu\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const serviceId = searchParams.get(\"serviceId\");\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const query = serviceId ? {\n            serviceId: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(serviceId)\n        } : {};\n        const packages = await db.collection(\"packages\").find(query).toArray();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            packages: packages.map((pkg)=>({\n                    id: pkg._id,\n                    serviceId: pkg.serviceId,\n                    type: pkg.type,\n                    name: pkg.name,\n                    hours: pkg.hours,\n                    maxStudents: pkg.maxStudents,\n                    minStudents: pkg.minStudents,\n                    price: pkg.price,\n                    details: pkg.details,\n                    isActive: pkg.isActive\n                }))\n        });\n    } catch (error) {\n        console.error(\"Paket listesi hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT metodu için\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID parametresi gerekli\"\n            }, {\n                status: 400\n            });\n        }\n        // ObjectId geçerliliği kontrolü\n        if (!mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId.isValid(id)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Ge\\xe7ersiz ID formatı\"\n            }, {\n                status: 400\n            });\n        }\n        const data = await request.json();\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        // Güncelleme verisi hazırlama\n        const updateData = {\n            type: data.type,\n            name: data.name?.trim(),\n            hours: Number(data.hours) || 0,\n            maxStudents: Number(data.maxStudents) || 1,\n            price: Number(data.price),\n            details: data.details?.trim() || \"\",\n            isActive: Boolean(data.isActive),\n            updatedAt: new Date()\n        };\n        // serviceId güncelleme\n        if (data.serviceId && data.serviceId !== \"default\" && mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId.isValid(data.serviceId)) {\n            updateData.serviceId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.serviceId);\n        }\n        if (data.serviceType) {\n            updateData.serviceType = data.serviceType;\n        }\n        if (data.minStudents !== undefined) {\n            updateData.minStudents = Number(data.minStudents);\n        }\n        const result = await db.collection(\"packages\").updateOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: updateData\n        });\n        if (result.matchedCount === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Paket bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Paket başarıyla g\\xfcncellendi\"\n        });\n    } catch (error) {\n        console.error(\"Paket g\\xfcncelleme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE metodu için\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID parametresi gerekli\"\n            }, {\n                status: 400\n            });\n        }\n        // ObjectId geçerliliği kontrolü\n        if (!mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId.isValid(id)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Ge\\xe7ersiz ID formatı\"\n            }, {\n                status: 400\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const result = await db.collection(\"packages\").deleteOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        });\n        if (result.deletedCount === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Paket bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Paket başarıyla silindi\"\n        });\n    } catch (error) {\n        console.error(\"Paket silme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"Paket silinirken bir hata oluştu\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/packages/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış.\");\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {};\nlet client;\nlet clientPromise;\nif (true) {\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpackages%2Froute&page=%2Fapi%2Fpackages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpackages%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();