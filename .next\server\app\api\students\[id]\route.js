"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/students/[id]/route";
exports.ids = ["app/api/students/[id]/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "./action-async-storage.external?8652":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external?0211":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external?137c":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_students_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/students/[id]/route.ts */ \"(rsc)/./src/app/api/students/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/students/[id]/route\",\n        pathname: \"/api/students/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/students/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\CURSOR-AI\\\\ai-project\\\\TintPlatesStdio\\\\src\\\\app\\\\api\\\\students\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_students_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/students/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/students/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/students/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_dbConnect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/dbConnect */ \"(rsc)/./src/lib/dbConnect.ts\");\n/* harmony import */ var _models_Student__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Student */ \"(rsc)/./src/models/Student.ts\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nasync function PUT(request, { params }) {\n    try {\n        // Oturum kontrolü\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        // ID kontrolü\n        if (!(0,mongoose__WEBPACK_IMPORTED_MODULE_5__.isValidObjectId)(params.id)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Ge\\xe7ersiz \\xf6ğrenci ID\"\n            }, {\n                status: 400\n            });\n        }\n        await (0,_lib_dbConnect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const body = await request.json();\n        // Öğrenci var mı kontrolü\n        const existingStudent = await _models_Student__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findById(params.id);\n        if (!existingStudent) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"\\xd6ğrenci bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        // Güncelleme işlemi\n        const updatedStudent = await _models_Student__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findByIdAndUpdate(params.id, {\n            $set: {\n                firstName: body.firstName,\n                lastName: body.lastName,\n                phone: body.phone,\n                email: body.email,\n                emergencyContact: {\n                    firstName: body.emergencyContact?.firstName || \"\",\n                    lastName: body.emergencyContact?.lastName || \"\",\n                    phone: body.emergencyContact?.phone || \"\",\n                    email: body.emergencyContact?.email || \"\"\n                },\n                healthInfo: {\n                    status: typeof body.healthInfo === \"string\" ? body.healthInfo : body.healthInfo?.status || \"Sağlıklı\",\n                    conditions: Array.isArray(body.healthInfo?.conditions) ? body.healthInfo.conditions : [],\n                    notes: body.healthInfo?.notes || \"\",\n                    bodyFatMeasurements: Array.isArray(body.healthInfo?.bodyFatMeasurements) ? body.healthInfo.bodyFatMeasurements : []\n                },\n                packages: Array.isArray(body.packages) ? body.packages : [],\n                isActive: typeof body.isActive === \"boolean\" ? body.isActive : true,\n                updatedAt: new Date()\n            }\n        }, {\n            new: true,\n            runValidators: true\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            student: updatedStudent\n        });\n    } catch (error) {\n        console.error(\"\\xd6ğrenci g\\xfcncelleme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"\\xd6ğrenci g\\xfcncellenirken bir hata oluştu\",\n            details:  true ? error.stack : 0\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        if (!(0,mongoose__WEBPACK_IMPORTED_MODULE_5__.isValidObjectId)(params.id)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Ge\\xe7ersiz \\xf6ğrenci ID\"\n            }, {\n                status: 400\n            });\n        }\n        await (0,_lib_dbConnect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const student = await _models_Student__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findById(params.id);\n        if (!student) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"\\xd6ğrenci bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        await _models_Student__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findByIdAndUpdate(params.id, {\n            isActive: false,\n            deactivatedAt: new Date()\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"\\xd6ğrenci silme hatası:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"\\xd6ğrenci silinirken bir hata oluştu\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/students/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"E-posta\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Şifre\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"E-posta ve şifre gerekli\");\n                    }\n                    const client = await _mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n                    const db = client.db(\"pilatesstudio\");\n                    // Önce users koleksiyonunda ara (admin için)\n                    let user = await db.collection(\"users\").findOne({\n                        email: credentials.email,\n                        isActive: true\n                    });\n                    // Kullanıcı bulunamazsa öğretmenler koleksiyonunda ara\n                    if (!user) {\n                        user = await db.collection(\"teachers\").findOne({\n                            email: credentials.email,\n                            isActive: true\n                        });\n                    }\n                    if (!user) {\n                        throw new Error(\"Kullanıcı bulunamadı\");\n                    }\n                    const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isValid) {\n                        throw new Error(\"Hatalı şifre\");\n                    }\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role || \"teacher\",\n                        color: user.color // Öğretmenler için renk kodu\n                    };\n                } catch (error) {\n                    throw error;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                if (user.color) token.color = user.color; // Öğretmen renk kodu\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                if (token.color) session.user.color = token.color; // Öğretmen renk kodu\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/login\"\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/dbConnect.ts":
/*!******************************!*\
  !*** ./src/lib/dbConnect.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış\");\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function dbConnect() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n// Bağlantıyı test et\nif (true) {\n    dbConnect().then(()=>console.log(\"Test bağlantısı başarılı\")).catch((error)=>console.error(\"Test bağlantısı başarısız:\", error));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/dbConnect.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış.\");\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {};\nlet client;\nlet clientPromise;\nif (true) {\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Student.ts":
/*!*******************************!*\
  !*** ./src/models/Student.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst bodyFatMeasurementSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    date: {\n        type: Date,\n        required: true\n    },\n    value: {\n        type: Number,\n        required: true\n    },\n    notes: String\n}, {\n    _id: false\n});\nconst healthInfoSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    status: {\n        type: String,\n        default: \"Sağlıklı\"\n    },\n    conditions: [\n        String\n    ],\n    notes: String,\n    bodyFatMeasurements: [\n        bodyFatMeasurementSchema\n    ]\n}, {\n    _id: false\n});\nconst studentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    studentId: {\n        type: String,\n        unique: true,\n        required: true\n    },\n    branchId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Branch\",\n        required: true\n    },\n    firstName: {\n        type: String,\n        required: true\n    },\n    lastName: {\n        type: String,\n        required: true\n    },\n    phone: {\n        type: String,\n        required: true\n    },\n    email: {\n        type: String,\n        required: true\n    },\n    emergencyContact: {\n        firstName: String,\n        lastName: String,\n        phone: String,\n        email: String,\n        relationship: String\n    },\n    healthInfo: healthInfoSchema,\n    startDate: {\n        type: Date,\n        default: Date.now\n    },\n    endDate: {\n        type: Date\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    teachers: [\n        {\n            type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n            ref: \"Teacher\"\n        }\n    ],\n    packages: [\n        {\n            type: {\n                type: String,\n                required: true\n            },\n            startDate: {\n                type: Date,\n                required: true\n            },\n            endDate: Date,\n            totalHours: {\n                type: Number,\n                required: true\n            },\n            remainingHours: {\n                type: Number,\n                required: true\n            },\n            price: {\n                type: Number,\n                required: true\n            },\n            paid: {\n                type: Number,\n                default: 0\n            },\n            status: {\n                type: String,\n                default: \"active\"\n            }\n        }\n    ],\n    deactivatedAt: Date,\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedAt: Date\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Student || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Student\", studentSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Student.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstudents%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();