!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactBigCalendar={})}(this,(function(e){"use strict";function t(e){return e.children}function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}function o(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){o(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function u(e,t){if(null==e)return{};var n,r,o=l(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}function f(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}function p(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(p=function(){return!!e})()}function v(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function h(e,t,n){return t=d(t),v(e,p()?Reflect.construct(t,n||[],d(e).constructor):t.apply(e,n))}function m(e,t){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},m(e,t)}function g(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&m(e,t)}function y(e){if(Array.isArray(e))return e}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function w(e,t){if(e){if("string"==typeof e)return b(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function k(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e,t){return y(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],u=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}}(e,t)||w(e,t)||k()}function S(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=S(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function D(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=S(e))&&(r&&(r+=" "),r+=t);return r}var _="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function x(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var O={},M={get exports(){return O},set exports(e){O=e}},C={},T=Object.getOwnPropertySymbols,P=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var N=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;var r=Object.getOwnPropertyNames(t).map((function(e){return t[e]}));if("0123456789"!==r.join(""))return!1;var o={};return"abcdefghijklmnopqrst".split("").forEach((function(e){o[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},o)).join("")}catch(e){return!1}}()?Object.assign:function(e,t){for(var n,r,o=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),a=1;a<arguments.length;a++){for(var i in n=Object(arguments[a]))P.call(n,i)&&(o[i]=n[i]);if(T){r=T(n);for(var l=0;l<r.length;l++)R.call(n,r[l])&&(o[r[l]]=n[r[l]])}}return o},z=N,L=60103,A=60106;
/** @license React v17.0.2
   * react.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */C.Fragment=60107,C.StrictMode=60108,C.Profiler=60114;var j=60109,F=60110,I=60112;C.Suspense=60113;var U=60115,W=60116;if("function"==typeof Symbol&&Symbol.for){var H=Symbol.for;L=H("react.element"),A=H("react.portal"),C.Fragment=H("react.fragment"),C.StrictMode=H("react.strict_mode"),C.Profiler=H("react.profiler"),j=H("react.provider"),F=H("react.context"),I=H("react.forward_ref"),C.Suspense=H("react.suspense"),U=H("react.memo"),W=H("react.lazy")}var V="function"==typeof Symbol&&Symbol.iterator;function B(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var $={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Y={};function q(e,t,n){this.props=e,this.context=t,this.refs=Y,this.updater=n||$}function K(){}function Q(e,t,n){this.props=e,this.context=t,this.refs=Y,this.updater=n||$}q.prototype.isReactComponent={},q.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(B(85));this.updater.enqueueSetState(this,e,t,"setState")},q.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},K.prototype=q.prototype;var G=Q.prototype=new K;G.constructor=Q,z(G,q.prototype),G.isPureReactComponent=!0;var X={current:null},J=Object.prototype.hasOwnProperty,Z={key:!0,ref:!0,__self:!0,__source:!0};function ee(e,t,n){var r,o={},a=null,i=null;if(null!=t)for(r in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(a=""+t.key),t)J.call(t,r)&&!Z.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){for(var u=Array(l),s=0;s<l;s++)u[s]=arguments[s+2];o.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===o[r]&&(o[r]=l[r]);return{$$typeof:L,type:e,key:a,ref:i,props:o,_owner:X.current}}function te(e){return"object"==typeof e&&null!==e&&e.$$typeof===L}var ne=/\/+/g;function re(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function oe(e,t,n,r,o){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var i=!1;if(null===e)i=!0;else switch(a){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case L:case A:i=!0}}if(i)return o=o(i=e),e=""===r?"."+re(i,0):r,Array.isArray(o)?(n="",null!=e&&(n=e.replace(ne,"$&/")+"/"),oe(o,t,n,"",(function(e){return e}))):null!=o&&(te(o)&&(o=function(e,t){return{$$typeof:L,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(ne,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=""===r?".":r+":",Array.isArray(e))for(var l=0;l<e.length;l++){var u=r+re(a=e[l],l);i+=oe(a,t,n,u,o)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=V&&e[V]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),l=0;!(a=e.next()).done;)i+=oe(a=a.value,t,n,u=r+re(a,l++),o);else if("object"===a)throw t=""+e,Error(B(31,"[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t));return i}function ae(e,t,n){if(null==e)return e;var r=[],o=0;return oe(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function ie(e){if(-1===e._status){var t=e._result;t=t(),e._status=0,e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}if(1===e._status)return e._result;throw e._result}var le={current:null};function ue(){var e=le.current;if(null===e)throw Error(B(321));return e}var se={ReactCurrentDispatcher:le,ReactCurrentBatchConfig:{transition:0},ReactCurrentOwner:X,IsSomeRendererActing:{current:!1},assign:z};C.Children={map:ae,forEach:function(e,t,n){ae(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return ae(e,(function(){t++})),t},toArray:function(e){return ae(e,(function(e){return e}))||[]},only:function(e){if(!te(e))throw Error(B(143));return e}},C.Component=q,C.PureComponent=Q,C.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=se,C.cloneElement=function(e,t,n){if(null==e)throw Error(B(267,e));var r=z({},e.props),o=e.key,a=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,i=X.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)J.call(t,u)&&!Z.hasOwnProperty(u)&&(r[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)r.children=n;else if(1<u){l=Array(u);for(var s=0;s<u;s++)l[s]=arguments[s+2];r.children=l}return{$$typeof:L,type:e.type,key:o,ref:a,props:r,_owner:i}},C.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:F,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:j,_context:e},e.Consumer=e},C.createElement=ee,C.createFactory=function(e){var t=ee.bind(null,e);return t.type=e,t},C.createRef=function(){return{current:null}},C.forwardRef=function(e){return{$$typeof:I,render:e}},C.isValidElement=te,C.lazy=function(e){return{$$typeof:W,_payload:{_status:-1,_result:e},_init:ie}},C.memo=function(e,t){return{$$typeof:U,type:e,compare:void 0===t?null:t}},C.useCallback=function(e,t){return ue().useCallback(e,t)},C.useContext=function(e,t){return ue().useContext(e,t)},C.useDebugValue=function(){},C.useEffect=function(e,t){return ue().useEffect(e,t)},C.useImperativeHandle=function(e,t,n){return ue().useImperativeHandle(e,t,n)},C.useLayoutEffect=function(e,t){return ue().useLayoutEffect(e,t)},C.useMemo=function(e,t){return ue().useMemo(e,t)},C.useReducer=function(e,t,n){return ue().useReducer(e,t,n)},C.useRef=function(e){return ue().useRef(e)},C.useState=function(e){return ue().useState(e)},C.version="17.0.2",function(e){e.exports=C}(M);var ce=x(O);function fe(){return fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},fe.apply(null,arguments)}var de=function(e,t,n,r,o,a,i,l){if(!e){var u;if(void 0===t)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[n,r,o,a,i,l],c=0;(u=new Error(t.replace(/%s/g,(function(){return s[c++]})))).name="Invariant Violation"}throw u.framesToPop=1,u}},pe=function(){};function ve(e,t){return void 0!==e[t]}function he(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function me(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function ge(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!=n?n:null}.bind(this))}function ye(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}me.__suppressDeprecationWarning=!0,ge.__suppressDeprecationWarning=!0,ye.__suppressDeprecationWarning=!0;var be={},we={get exports(){return be},set exports(e){be=e}};function ke(){}function Ee(){}Ee.resetWarningCache=ke;we.exports=function(){function e(e,t,n,r,o,a){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==a){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Ee,resetWarningCache:ke};return n.PropTypes=n,n}();var Se={PREVIOUS:"PREV",NEXT:"NEXT",TODAY:"TODAY",DATE:"DATE"},De={MONTH:"month",WEEK:"week",WORK_WEEK:"work_week",DAY:"day",AGENDA:"agenda"},_e=Object.keys(De).map((function(e){return De[e]}));be.oneOfType([be.string,be.func]),be.any,be.func,be.oneOfType([be.arrayOf(be.oneOf(_e)),be.objectOf((function(e,t){if(-1!==_e.indexOf(t)&&"boolean"==typeof e[t])return null;for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];return be.elementType.apply(be,[e,t].concat(r))}))]),be.oneOfType([be.oneOf(["overlap","no-overlap"]),be.func]);var xe="milliseconds",Oe="seconds",Me="minutes",Ce="hours",Te="day",Pe="week",Re="month",Ne="year",ze="decade",Le="century",Ae={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,day:864e5,week:6048e5},je={month:1,year:12,decade:120,century:1200};function Fe(e){return e%4==0&&e%100!=0||e%400==0?29:28}function Ie(e,t,n){switch(e=new Date(e),n){case xe:case Oe:case Me:case Ce:case Te:case Pe:return function(e,t){var n=new Date(+e+t);return function(e,t){var n=e.getTimezoneOffset(),r=t.getTimezoneOffset();return new Date(+t+(r-n)*Ae.minutes)}(e,n)}(e,t*Ae[n]);case Re:case Ne:case ze:case Le:return function(e,t){var n=e.getFullYear(),r=e.getMonth(),o=e.getDate(),a=12*n+r+t,i=Math.trunc(a/12),l=a%12,u=Math.min(o,function(e){return[31,Fe(e),31,30,31,30,31,31,30,31,30,31]}(i)[l]),s=new Date(e);return s.setFullYear(i),s.setDate(1),s.setMonth(l),s.setDate(u),s}(e,t*je[n])}throw new TypeError('Invalid units: "'+n+'"')}function Ue(e,t,n){return Ie(e,-t,n)}function We(e,t,n){switch(e=new Date(e),t){case Le:case ze:case Ne:e=ot(e,0);case Re:e=rt(e,1);case Pe:case Te:e=tt(e,0);case Ce:e=et(e,0);case Me:e=Ze(e,0);case Oe:e=Je(e,0)}return t===ze&&(e=Ue(e,at(e)%10,"year")),t===Le&&(e=Ue(e,at(e)%100,"year")),t===Pe&&(e=it(e,0,n)),e}function He(e,t,n){switch(e=We(e=new Date(e),t,n),t){case Le:case ze:case Ne:case Re:case Pe:(e=Ue(e=Ie(e,1,t),1,Te)).setHours(23,59,59,999);break;case Te:e.setHours(23,59,59,999);break;case Ce:case Me:case Oe:e=Ue(e=Ie(e,1,t),1,xe)}return e}var Ve=ut((function(e,t){return e===t})),Be=ut((function(e,t){return e!==t})),$e=ut((function(e,t){return e>t})),Ye=ut((function(e,t){return e>=t})),qe=ut((function(e,t){return e<t})),Ke=ut((function(e,t){return e<=t}));function Qe(){return new Date(Math.min.apply(Math,arguments))}function Ge(){return new Date(Math.max.apply(Math,arguments))}function Xe(e,t,n,r){return r=r||"day",(!t||Ye(e,t,r))&&(!n||Ke(e,n,r))}var Je=lt("Milliseconds"),Ze=lt("Seconds"),et=lt("Minutes"),tt=lt("Hours"),nt=lt("Day"),rt=lt("Date"),ot=lt("Month"),at=lt("FullYear");function it(e,t,n){var r=(nt(e)+7-(n||0))%7;return void 0===t?r:Ie(e,t-r,Te)}function lt(e){var t=function(e){switch(e){case"Milliseconds":return 36e5;case"Seconds":return 3600;case"Minutes":return 60;case"Hours":return 1;default:return null}}(e);return function(n,r){if(void 0===r)return n["get"+e]();var o=new Date(n);return o["set"+e](r),t&&o["get"+e]()!=r&&("Hours"===e||r>=t&&o.getHours()-n.getHours()<Math.floor(r/t))&&o["set"+e](r+t),o}}function ut(e){return function(t,n,r){return e(+We(t,r),+We(n,r))}}var st=Object.freeze({__proto__:null,add:Ie,century:function(e,t){return void 0===t?at(We(e,Le)):Ie(e,t+100,Ne)},date:rt,day:nt,decade:function(e,t){return void 0===t?at(We(e,ze)):Ie(e,t+10,Ne)},diff:function(e,t,n,r){var o,a,i;switch(n){case xe:case Oe:case Me:case Ce:case Te:case Pe:o=t.getTime()-e.getTime();break;case Re:case Ne:case ze:case Le:o=12*(at(t)-at(e))+ot(t)-ot(e);break;default:throw new TypeError('Invalid units: "'+n+'"')}switch(n){case xe:a=1;break;case Oe:a=1e3;break;case Me:a=6e4;break;case Ce:a=36e5;break;case Te:a=864e5;break;case Pe:a=6048e5;break;case Re:a=1;break;case Ne:a=12;break;case ze:a=120;break;case Le:a=1200;break;default:throw new TypeError('Invalid units: "'+n+'"')}return i=o/a,r?i:Math.round(i)},endOf:He,eq:Ve,gt:$e,gte:Ye,hours:tt,inRange:Xe,lt:qe,lte:Ke,max:Ge,milliseconds:Je,min:Qe,minutes:et,month:ot,neq:Be,seconds:Ze,startOf:We,subtract:Ue,weekday:it,year:at}),ct={seconds:1e3,minutes:6e4,hours:36e5,day:864e5};function ft(e,t){var n=We(e,"month");return We(n,"week",t.startOfWeek())}function dt(e,t){var n=He(e,"month");return He(n,"week",t.startOfWeek())}function pt(e,t){for(var n=ft(e,t),r=dt(e,t),o=[];Ke(n,r,"day");)o.push(n),n=Ie(n,1,"day");return o}function vt(e,t){var n=We(e,t);return Ve(n,e)?n:Ie(n,1,t)}function ht(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day",r=e,o=[];Ke(r,t,n);)o.push(r),r=Ie(r,1,n);return o}function mt(e,t){return null==t&&null==e?null:(null==t&&(t=new Date),null==e&&(e=new Date),e=We(e,"day"),e=tt(e,tt(t)),e=et(e,et(t)),e=Ze(e,Ze(t)),Je(e,Je(t)))}function gt(e){return 0===tt(e)&&0===et(e)&&0===Ze(e)&&0===Je(e)}function yt(e,t,n){return n&&"milliseconds"!==n?Math.round(Math.abs(+We(e,n)/ct[n]-+We(t,n)/ct[n])):Math.abs(+e-+t)}var bt=be.oneOfType([be.string,be.func]);function wt(e,t,n,r,o){var a="function"==typeof r?r(n,o,e):t.call(e,n,r,o);return de(null==a||"string"==typeof a,"`localizer format(..)` must return a string, null, or undefined"),a}function kt(e,t,n){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,t+n,0,0)}function Et(e,t){return e.getTimezoneOffset()-t.getTimezoneOffset()}function St(e,t){return yt(e,t,"minutes")+Et(e,t)}function Dt(e){var t=We(e,"day");return yt(t,e,"minutes")+Et(t,e)}function _t(e,t){return qe(e,t,"day")}function xt(e,t,n){return Ve(e,t,"minutes")?Ye(t,n,"minutes"):$e(t,n,"minutes")}function Ot(e,t){return function(e,t,n,r){return"day"===n&&(n="date"),Math.abs(st[n](e,void 0,r)-st[n](t,void 0,r))}(e,t,"day")}function Mt(e){var t=e.evtA,n=t.start,r=t.end,o=t.allDay,a=e.evtB,i=a.start,l=a.end,u=a.allDay,s=+We(n,"day")-+We(i,"day"),c=Ot(n,r),f=Ot(i,l);return s||f-c||!!u-!!o||+n-+i||+r-+l}function Ct(e){var t=e.event,n=t.start,r=t.end,o=e.range,a=o.start,i=o.end,l=We(n,"day"),u=Ke(l,i,"day"),s=Be(l,r,"minutes")?$e(r,a,"minutes"):Ye(r,a,"minutes");return u&&s}function Tt(e,t){return Ve(e,t,"day")}function Pt(e,t){return gt(e)&&gt(t)}var Rt=f((function e(t){var n=this;s(this,e),de("function"==typeof t.format,"date localizer `format(..)` must be a function"),de("function"==typeof t.firstOfWeek,"date localizer `firstOfWeek(..)` must be a function"),this.propType=t.propType||bt,this.formats=t.formats,this.format=function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return wt.apply(void 0,[n,t.format].concat(r))},this.startOfWeek=t.firstOfWeek,this.merge=t.merge||mt,this.inRange=t.inRange||Xe,this.lt=t.lt||qe,this.lte=t.lte||Ke,this.gt=t.gt||$e,this.gte=t.gte||Ye,this.eq=t.eq||Ve,this.neq=t.neq||Be,this.startOf=t.startOf||We,this.endOf=t.endOf||He,this.add=t.add||Ie,this.range=t.range||ht,this.diff=t.diff||yt,this.ceil=t.ceil||vt,this.min=t.min||Qe,this.max=t.max||Ge,this.minutes=t.minutes||et,this.daySpan=t.daySpan||Ot,this.firstVisibleDay=t.firstVisibleDay||ft,this.lastVisibleDay=t.lastVisibleDay||dt,this.visibleDays=t.visibleDays||pt,this.getSlotDate=t.getSlotDate||kt,this.getTimezoneOffset=t.getTimezoneOffset||function(e){return e.getTimezoneOffset()},this.getDstOffset=t.getDstOffset||Et,this.getTotalMin=t.getTotalMin||St,this.getMinutesFromMidnight=t.getMinutesFromMidnight||Dt,this.continuesPrior=t.continuesPrior||_t,this.continuesAfter=t.continuesAfter||xt,this.sortEvents=t.sortEvents||Mt,this.inEventRange=t.inEventRange||Ct,this.isSameDate=t.isSameDate||Tt,this.startAndEndAreDateOnly=t.startAndEndAreDateOnly||Pt,this.segmentOffset=t.browserTZOffset?t.browserTZOffset():0}));function Nt(e,t,n,r){var o=i(i({},e.formats),n);return i(i({},e),{},{messages:r,startOfWeek:function(){return e.startOfWeek(t)},format:function(n,r){return e.format(n,o[r]||r,t)}})}var zt=function(e){function t(){var e;s(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=h(this,t,[].concat(r))).navigate=function(t){e.props.onNavigate(t)},e.view=function(t){e.props.onView(t)},e}return g(t,e),f(t,[{key:"render",value:function(){var e=this.props,t=e.localizer.messages,n=e.label;return ce.createElement("div",{className:"rbc-toolbar"},ce.createElement("span",{className:"rbc-btn-group"},ce.createElement("button",{type:"button",onClick:this.navigate.bind(null,Se.TODAY)},t.today),ce.createElement("button",{type:"button",onClick:this.navigate.bind(null,Se.PREVIOUS)},t.previous),ce.createElement("button",{type:"button",onClick:this.navigate.bind(null,Se.NEXT)},t.next)),ce.createElement("span",{className:"rbc-toolbar-label"},n),ce.createElement("span",{className:"rbc-btn-group"},this.viewNamesGroup(t)))}},{key:"viewNamesGroup",value:function(e){var t=this,n=this.props.views,r=this.props.view;if(n.length>1)return n.map((function(n){return ce.createElement("button",{type:"button",key:n,className:D({"rbc-active":r===n}),onClick:t.view.bind(null,n)},e[n])}))}}])}(ce.Component);function Lt(e,t){e&&e.apply(null,[].concat(t))}var At={date:"Date",time:"Time",event:"Event",allDay:"All Day",week:"Week",work_week:"Work Week",day:"Day",month:"Month",previous:"Back",next:"Next",yesterday:"Yesterday",tomorrow:"Tomorrow",today:"Today",agenda:"Agenda",noEventsInRange:"There are no events in this range.",showMore:function(e){return"+".concat(e," more")}};function jt(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function Ft(e){return function(e){if(Array.isArray(e))return b(e)}(e)||jt(e)||w(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var It=function(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var a=Array(o);++r<o;)a[r]=e[r+t];return a};var Ut=function(e,t){return e===t||e!=e&&t!=t},Wt="object"==typeof _&&_&&_.Object===Object&&_,Ht=Wt,Vt="object"==typeof self&&self&&self.Object===Object&&self,Bt=Ht||Vt||Function("return this")(),$t=Bt.Symbol,Yt=$t,qt=Object.prototype,Kt=qt.hasOwnProperty,Qt=qt.toString,Gt=Yt?Yt.toStringTag:void 0;var Xt=function(e){var t=Kt.call(e,Gt),n=e[Gt];try{e[Gt]=void 0;var r=!0}catch(e){}var o=Qt.call(e);return r&&(t?e[Gt]=n:delete e[Gt]),o},Jt=Object.prototype.toString;var Zt=Xt,en=function(e){return Jt.call(e)},tn=$t?$t.toStringTag:void 0;var nn=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":tn&&tn in Object(e)?Zt(e):en(e)};var rn=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},on=nn,an=rn;var ln=function(e){if(!an(e))return!1;var t=on(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};var un=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},sn=ln,cn=un;var fn=function(e){return null!=e&&cn(e.length)&&!sn(e)},dn=/^(?:0|[1-9]\d*)$/;var pn=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&dn.test(e))&&e>-1&&e%1==0&&e<t},vn=Ut,hn=fn,mn=pn,gn=rn;var yn=function(e,t,n){if(!gn(n))return!1;var r=typeof t;return!!("number"==r?hn(n)&&mn(t,n.length):"string"==r&&t in n)&&vn(n[t],e)},bn=/\s/;var wn=function(e){for(var t=e.length;t--&&bn.test(e.charAt(t)););return t},kn=/^\s+/;var En=function(e){return null!=e&&"object"==typeof e},Sn=nn,Dn=En;var _n=function(e){return"symbol"==typeof e||Dn(e)&&"[object Symbol]"==Sn(e)},xn=function(e){return e?e.slice(0,wn(e)+1).replace(kn,""):e},On=rn,Mn=_n,Cn=/^[-+]0x[0-9a-f]+$/i,Tn=/^0b[01]+$/i,Pn=/^0o[0-7]+$/i,Rn=parseInt;var Nn=function(e){if("number"==typeof e)return e;if(Mn(e))return NaN;if(On(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=On(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=xn(e);var n=Tn.test(e);return n||Pn.test(e)?Rn(e.slice(2),n?2:8):Cn.test(e)?NaN:+e},zn=1/0;var Ln=function(e){return e?(e=Nn(e))===zn||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0},An=Ln;var jn=function(e){var t=An(e),n=t%1;return t==t?n?t-n:t:0},Fn=It,In=yn,Un=jn,Wn=Math.ceil,Hn=Math.max;var Vn=function(e,t,n){t=(n?In(e,t,n):void 0===t)?1:Hn(Un(t),0);var r=null==e?0:e.length;if(!r||t<1)return[];for(var o=0,a=0,i=Array(Wn(r/t));o<r;)i[a++]=Fn(e,o,o+=t);return i};function Bn(e){return e&&e.ownerDocument||document}function $n(e,t){return function(e){var t=Bn(e);return t&&t.defaultView||window}(e).getComputedStyle(e,t)}var Yn=/([A-Z])/g;var qn=/^ms-/;function Kn(e){return function(e){return e.replace(Yn,"-$1").toLowerCase()}(e).replace(qn,"-ms-")}var Qn=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;function Gn(e,t){var n="",r="";if("string"==typeof t)return e.style.getPropertyValue(Kn(t))||$n(e).getPropertyValue(Kn(t));Object.keys(t).forEach((function(o){var a=t[o];a||0===a?!function(e){return!(!e||!Qn.test(e))}(o)?n+=Kn(o)+": "+a+";":r+=o+"("+a+") ":e.style.removeProperty(Kn(o))})),r&&(n+="transform: "+r+";"),e.style.cssText+=";"+n}function Xn(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}function Jn(e){return"window"in e&&e.window===e?e:"nodeType"in(t=e)&&t.nodeType===document.DOCUMENT_NODE&&e.defaultView||!1;var t}function Zn(e){var t="pageXOffset"===e?"scrollLeft":"scrollTop";return function(n,r){var o=Jn(n);if(void 0===r)return o?o[e]:n[t];o?o.scrollTo(o[e],r):n[t]=r}}var er=Zn("pageXOffset"),tr=Zn("pageYOffset");function nr(e){var t=Bn(e),n={top:0,left:0,height:0,width:0},r=t&&t.documentElement;return r&&Xn(r,e)?(void 0!==e.getBoundingClientRect&&(n=e.getBoundingClientRect()),n={top:n.top+tr(r)-(r.clientTop||0),left:n.left+er(r)-(r.clientLeft||0),width:n.width,height:n.height}):n}var rr=function(e){return!!e&&"offsetParent"in e};function or(e,t){var n,r={top:0,left:0};if("fixed"===Gn(e,"position"))n=e.getBoundingClientRect();else{var o=t||function(e){for(var t=Bn(e),n=e&&e.offsetParent;rr(n)&&"HTML"!==n.nodeName&&"static"===Gn(n,"position");)n=n.offsetParent;return n||t.documentElement}(e);n=nr(e),"html"!==function(e){return e.nodeName&&e.nodeName.toLowerCase()}(o)&&(r=nr(o));var a=String(Gn(o,"borderTopWidth")||0);r.top+=parseInt(a,10)-tr(o)||0;var i=String(Gn(o,"borderLeftWidth")||0);r.left+=parseInt(i,10)-er(o)||0}var l=String(Gn(e,"marginTop")||0),u=String(Gn(e,"marginLeft")||0);return fe({},n,{top:n.top-r.top-(parseInt(l,10)||0),left:n.left-r.left-(parseInt(u,10)||0)})}var ar=!("undefined"==typeof window||!window.document||!window.document.createElement),ir=(new Date).getTime();var lr="clearTimeout",ur=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-ir)),r=setTimeout(e,n);return ir=t,r},sr=function(e,t){return e+(e?t[0].toUpperCase()+t.substr(1):t)+"AnimationFrame"};ar&&["","webkit","moz","o","ms"].some((function(e){var t=sr(e,"request");return t in window&&(lr=sr(e,"cancel"),ur=function(e){return window[t](e)}),!!ur}));var cr,fr=function(e){"function"==typeof window[lr]&&window[lr](e)},dr=ur;function pr(e,t){if(!cr){var n=document.body,r=n.matches||n.matchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector||n.msMatchesSelector;cr=function(e,t){return r.call(e,t)}}return cr(e,t)}var vr=Function.prototype.bind.call(Function.prototype.call,[].slice);var hr=!1,mr=!1;try{var gr={get passive(){return hr=!0},get once(){return mr=hr=!0}};ar&&(window.addEventListener("test",gr,gr),window.removeEventListener("test",gr,!0))}catch(e){}function yr(e){const t=function(e){const t=O.useRef(e);return O.useEffect((()=>{t.current=e}),[e]),t}(e);return O.useCallback((function(...e){return t.current&&t.current(...e)}),[t])}function br(){return O.useState(null)}function wr(e){const t=function(){const e=O.useRef(!0),t=O.useRef((()=>e.current));return O.useEffect((()=>(e.current=!0,()=>{e.current=!1})),[]),t.current}();return[e[0],O.useCallback((n=>{if(t())return e[1](n)}),[t,e[1]])]}var kr="top",Er="bottom",Sr="right",Dr="left",_r="auto",xr=[kr,Er,Sr,Dr],Or="start",Mr="end",Cr="clippingParents",Tr="viewport",Pr="popper",Rr="reference",Nr=xr.reduce((function(e,t){return e.concat([t+"-"+Or,t+"-"+Mr])}),[]),zr=[].concat(xr,[_r]).reduce((function(e,t){return e.concat([t,t+"-"+Or,t+"-"+Mr])}),[]),Lr=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Ar(e){return e.split("-")[0]}function jr(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Fr(e){return e instanceof jr(e).Element||e instanceof Element}function Ir(e){return e instanceof jr(e).HTMLElement||e instanceof HTMLElement}function Ur(e){return"undefined"!=typeof ShadowRoot&&(e instanceof jr(e).ShadowRoot||e instanceof ShadowRoot)}var Wr=Math.max,Hr=Math.min,Vr=Math.round;function Br(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function $r(){return!/^((?!chrome|android).)*safari/i.test(Br())}function Yr(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,a=1;t&&Ir(e)&&(o=e.offsetWidth>0&&Vr(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&Vr(r.height)/e.offsetHeight||1);var i=(Fr(e)?jr(e):window).visualViewport,l=!$r()&&n,u=(r.left+(l&&i?i.offsetLeft:0))/o,s=(r.top+(l&&i?i.offsetTop:0))/a,c=r.width/o,f=r.height/a;return{width:c,height:f,top:s,right:u+c,bottom:s+f,left:u,x:u,y:s}}function qr(e){var t=Yr(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Kr(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ur(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Qr(e){return e?(e.nodeName||"").toLowerCase():null}function Gr(e){return jr(e).getComputedStyle(e)}function Xr(e){return["table","td","th"].indexOf(Qr(e))>=0}function Jr(e){return((Fr(e)?e.ownerDocument:e.document)||window.document).documentElement}function Zr(e){return"html"===Qr(e)?e:e.assignedSlot||e.parentNode||(Ur(e)?e.host:null)||Jr(e)}function eo(e){return Ir(e)&&"fixed"!==Gr(e).position?e.offsetParent:null}function to(e){for(var t=jr(e),n=eo(e);n&&Xr(n)&&"static"===Gr(n).position;)n=eo(n);return n&&("html"===Qr(n)||"body"===Qr(n)&&"static"===Gr(n).position)?t:n||function(e){var t=/firefox/i.test(Br());if(/Trident/i.test(Br())&&Ir(e)&&"fixed"===Gr(e).position)return null;var n=Zr(e);for(Ur(n)&&(n=n.host);Ir(n)&&["html","body"].indexOf(Qr(n))<0;){var r=Gr(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function no(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function ro(e,t,n){return Wr(e,Hr(t,n))}function oo(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function ao(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var io={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,l=Ar(n.placement),u=no(l),s=[Dr,Sr].indexOf(l)>=0?"height":"width";if(a&&i){var c=function(e,t){return oo("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:ao(e,xr))}(o.padding,n),f=qr(a),d="y"===u?kr:Dr,p="y"===u?Er:Sr,v=n.rects.reference[s]+n.rects.reference[u]-i[u]-n.rects.popper[s],h=i[u]-n.rects.reference[u],m=to(a),g=m?"y"===u?m.clientHeight||0:m.clientWidth||0:0,y=v/2-h/2,b=c[d],w=g-f[s]-c[p],k=g/2-f[s]/2+y,E=ro(b,k,w),S=u;n.modifiersData[r]=((t={})[S]=E,t.centerOffset=E-k,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Kr(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function lo(e){return e.split("-")[1]}var uo={top:"auto",right:"auto",bottom:"auto",left:"auto"};function so(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,l=e.position,u=e.gpuAcceleration,s=e.adaptive,c=e.roundOffsets,f=e.isFixed,d=i.x,p=void 0===d?0:d,v=i.y,h=void 0===v?0:v,m="function"==typeof c?c({x:p,y:h}):{x:p,y:h};p=m.x,h=m.y;var g=i.hasOwnProperty("x"),y=i.hasOwnProperty("y"),b=Dr,w=kr,k=window;if(s){var E=to(n),S="clientHeight",D="clientWidth";if(E===jr(n)&&"static"!==Gr(E=Jr(n)).position&&"absolute"===l&&(S="scrollHeight",D="scrollWidth"),o===kr||(o===Dr||o===Sr)&&a===Mr)w=Er,h-=(f&&E===k&&k.visualViewport?k.visualViewport.height:E[S])-r.height,h*=u?1:-1;if(o===Dr||(o===kr||o===Er)&&a===Mr)b=Sr,p-=(f&&E===k&&k.visualViewport?k.visualViewport.width:E[D])-r.width,p*=u?1:-1}var _,x=Object.assign({position:l},s&&uo),O=!0===c?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:Vr(n*o)/o||0,y:Vr(r*o)/o||0}}({x:p,y:h},jr(n)):{x:p,y:h};return p=O.x,h=O.y,u?Object.assign({},x,((_={})[w]=y?"0":"",_[b]=g?"0":"",_.transform=(k.devicePixelRatio||1)<=1?"translate("+p+"px, "+h+"px)":"translate3d("+p+"px, "+h+"px, 0)",_)):Object.assign({},x,((t={})[w]=y?h+"px":"",t[b]=g?p+"px":"",t.transform="",t))}var co={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,i=void 0===a||a,l=n.roundOffsets,u=void 0===l||l,s={placement:Ar(t.placement),variation:lo(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,so(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:u})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,so(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:u})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},fo={passive:!0};var po={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,i=r.resize,l=void 0===i||i,u=jr(t.elements.popper),s=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&s.forEach((function(e){e.addEventListener("scroll",n.update,fo)})),l&&u.addEventListener("resize",n.update,fo),function(){a&&s.forEach((function(e){e.removeEventListener("scroll",n.update,fo)})),l&&u.removeEventListener("resize",n.update,fo)}},data:{}},vo={left:"right",right:"left",bottom:"top",top:"bottom"};function ho(e){return e.replace(/left|right|bottom|top/g,(function(e){return vo[e]}))}var mo={start:"end",end:"start"};function go(e){return e.replace(/start|end/g,(function(e){return mo[e]}))}function yo(e){var t=jr(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function bo(e){return Yr(Jr(e)).left+yo(e).scrollLeft}function wo(e){var t=Gr(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function ko(e){return["html","body","#document"].indexOf(Qr(e))>=0?e.ownerDocument.body:Ir(e)&&wo(e)?e:ko(Zr(e))}function Eo(e,t){var n;void 0===t&&(t=[]);var r=ko(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=jr(r),i=o?[a].concat(a.visualViewport||[],wo(r)?r:[]):r,l=t.concat(i);return o?l:l.concat(Eo(Zr(i)))}function So(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Do(e,t,n){return t===Tr?So(function(e,t){var n=jr(e),r=Jr(e),o=n.visualViewport,a=r.clientWidth,i=r.clientHeight,l=0,u=0;if(o){a=o.width,i=o.height;var s=$r();(s||!s&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:a,height:i,x:l+bo(e),y:u}}(e,n)):Fr(t)?function(e,t){var n=Yr(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):So(function(e){var t,n=Jr(e),r=yo(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=Wr(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=Wr(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),l=-r.scrollLeft+bo(e),u=-r.scrollTop;return"rtl"===Gr(o||n).direction&&(l+=Wr(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:l,y:u}}(Jr(e)))}function _o(e,t,n,r){var o="clippingParents"===t?function(e){var t=Eo(Zr(e)),n=["absolute","fixed"].indexOf(Gr(e).position)>=0&&Ir(e)?to(e):e;return Fr(n)?t.filter((function(e){return Fr(e)&&Kr(e,n)&&"body"!==Qr(e)})):[]}(e):[].concat(t),a=[].concat(o,[n]),i=a[0],l=a.reduce((function(t,n){var o=Do(e,n,r);return t.top=Wr(o.top,t.top),t.right=Hr(o.right,t.right),t.bottom=Hr(o.bottom,t.bottom),t.left=Wr(o.left,t.left),t}),Do(e,i,r));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function xo(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?Ar(o):null,i=o?lo(o):null,l=n.x+n.width/2-r.width/2,u=n.y+n.height/2-r.height/2;switch(a){case kr:t={x:l,y:n.y-r.height};break;case Er:t={x:l,y:n.y+n.height};break;case Sr:t={x:n.x+n.width,y:u};break;case Dr:t={x:n.x-r.width,y:u};break;default:t={x:n.x,y:n.y}}var s=a?no(a):null;if(null!=s){var c="y"===s?"height":"width";switch(i){case Or:t[s]=t[s]-(n[c]/2-r[c]/2);break;case Mr:t[s]=t[s]+(n[c]/2-r[c]/2)}}return t}function Oo(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.strategy,i=void 0===a?e.strategy:a,l=n.boundary,u=void 0===l?Cr:l,s=n.rootBoundary,c=void 0===s?Tr:s,f=n.elementContext,d=void 0===f?Pr:f,p=n.altBoundary,v=void 0!==p&&p,h=n.padding,m=void 0===h?0:h,g=oo("number"!=typeof m?m:ao(m,xr)),y=d===Pr?Rr:Pr,b=e.rects.popper,w=e.elements[v?y:d],k=_o(Fr(w)?w:w.contextElement||Jr(e.elements.popper),u,c,i),E=Yr(e.elements.reference),S=xo({reference:E,element:b,strategy:"absolute",placement:o}),D=So(Object.assign({},b,S)),_=d===Pr?D:E,x={top:k.top-_.top+g.top,bottom:_.bottom-k.bottom+g.bottom,left:k.left-_.left+g.left,right:_.right-k.right+g.right},O=e.modifiersData.offset;if(d===Pr&&O){var M=O[o];Object.keys(x).forEach((function(e){var t=[Sr,Er].indexOf(e)>=0?1:-1,n=[kr,Er].indexOf(e)>=0?"y":"x";x[e]+=M[n]*t}))}return x}function Mo(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,i=n.padding,l=n.flipVariations,u=n.allowedAutoPlacements,s=void 0===u?zr:u,c=lo(r),f=c?l?Nr:Nr.filter((function(e){return lo(e)===c})):xr,d=f.filter((function(e){return s.indexOf(e)>=0}));0===d.length&&(d=f);var p=d.reduce((function(t,n){return t[n]=Oo(e,{placement:n,boundary:o,rootBoundary:a,padding:i})[Ar(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}var Co={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,i=n.altAxis,l=void 0===i||i,u=n.fallbackPlacements,s=n.padding,c=n.boundary,f=n.rootBoundary,d=n.altBoundary,p=n.flipVariations,v=void 0===p||p,h=n.allowedAutoPlacements,m=t.options.placement,g=Ar(m),y=u||(g===m||!v?[ho(m)]:function(e){if(Ar(e)===_r)return[];var t=ho(e);return[go(e),t,go(t)]}(m)),b=[m].concat(y).reduce((function(e,n){return e.concat(Ar(n)===_r?Mo(t,{placement:n,boundary:c,rootBoundary:f,padding:s,flipVariations:v,allowedAutoPlacements:h}):n)}),[]),w=t.rects.reference,k=t.rects.popper,E=new Map,S=!0,D=b[0],_=0;_<b.length;_++){var x=b[_],O=Ar(x),M=lo(x)===Or,C=[kr,Er].indexOf(O)>=0,T=C?"width":"height",P=Oo(t,{placement:x,boundary:c,rootBoundary:f,altBoundary:d,padding:s}),R=C?M?Sr:Dr:M?Er:kr;w[T]>k[T]&&(R=ho(R));var N=ho(R),z=[];if(a&&z.push(P[O]<=0),l&&z.push(P[R]<=0,P[N]<=0),z.every((function(e){return e}))){D=x,S=!1;break}E.set(x,z)}if(S)for(var L=function(e){var t=b.find((function(t){var n=E.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return D=t,"break"},A=v?3:1;A>0;A--){if("break"===L(A))break}t.placement!==D&&(t.modifiersData[r]._skip=!0,t.placement=D,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function To(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Po(e){return[kr,Sr,Er,Dr].some((function(t){return e[t]>=0}))}var Ro={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,i=zr.reduce((function(e,n){return e[n]=function(e,t,n){var r=Ar(e),o=[Dr,kr].indexOf(r)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],l=a[1];return i=i||0,l=(l||0)*o,[Dr,Sr].indexOf(r)>=0?{x:l,y:i}:{x:i,y:l}}(n,t.rects,a),e}),{}),l=i[t.placement],u=l.x,s=l.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=u,t.modifiersData.popperOffsets.y+=s),t.modifiersData[r]=i}};var No={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,i=n.altAxis,l=void 0!==i&&i,u=n.boundary,s=n.rootBoundary,c=n.altBoundary,f=n.padding,d=n.tether,p=void 0===d||d,v=n.tetherOffset,h=void 0===v?0:v,m=Oo(t,{boundary:u,rootBoundary:s,padding:f,altBoundary:c}),g=Ar(t.placement),y=lo(t.placement),b=!y,w=no(g),k="x"===w?"y":"x",E=t.modifiersData.popperOffsets,S=t.rects.reference,D=t.rects.popper,_="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,x="number"==typeof _?{mainAxis:_,altAxis:_}:Object.assign({mainAxis:0,altAxis:0},_),O=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,M={x:0,y:0};if(E){if(a){var C,T="y"===w?kr:Dr,P="y"===w?Er:Sr,R="y"===w?"height":"width",N=E[w],z=N+m[T],L=N-m[P],A=p?-D[R]/2:0,j=y===Or?S[R]:D[R],F=y===Or?-D[R]:-S[R],I=t.elements.arrow,U=p&&I?qr(I):{width:0,height:0},W=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},H=W[T],V=W[P],B=ro(0,S[R],U[R]),$=b?S[R]/2-A-B-H-x.mainAxis:j-B-H-x.mainAxis,Y=b?-S[R]/2+A+B+V+x.mainAxis:F+B+V+x.mainAxis,q=t.elements.arrow&&to(t.elements.arrow),K=q?"y"===w?q.clientTop||0:q.clientLeft||0:0,Q=null!=(C=null==O?void 0:O[w])?C:0,G=N+Y-Q,X=ro(p?Hr(z,N+$-Q-K):z,N,p?Wr(L,G):L);E[w]=X,M[w]=X-N}if(l){var J,Z="x"===w?kr:Dr,ee="x"===w?Er:Sr,te=E[k],ne="y"===k?"height":"width",re=te+m[Z],oe=te-m[ee],ae=-1!==[kr,Dr].indexOf(g),ie=null!=(J=null==O?void 0:O[k])?J:0,le=ae?re:te-S[ne]-D[ne]-ie+x.altAxis,ue=ae?te+S[ne]+D[ne]-ie-x.altAxis:oe,se=p&&ae?function(e,t,n){var r=ro(e,t,n);return r>n?n:r}(le,te,ue):ro(p?le:re,te,p?ue:oe);E[k]=se,M[k]=se-te}t.modifiersData[r]=M}},requiresIfExists:["offset"]};function zo(e,t,n){void 0===n&&(n=!1);var r,o,a=Ir(t),i=Ir(t)&&function(e){var t=e.getBoundingClientRect(),n=Vr(t.width)/e.offsetWidth||1,r=Vr(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),l=Jr(t),u=Yr(e,i,n),s={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(a||!a&&!n)&&(("body"!==Qr(t)||wo(l))&&(s=(r=t)!==jr(r)&&Ir(r)?{scrollLeft:(o=r).scrollLeft,scrollTop:o.scrollTop}:yo(r)),Ir(t)?((c=Yr(t,!0)).x+=t.clientLeft,c.y+=t.clientTop):l&&(c.x=bo(l))),{x:u.left+s.scrollLeft-c.x,y:u.top+s.scrollTop-c.y,width:u.width,height:u.height}}function Lo(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}var Ao={placement:"bottom",modifiers:[],strategy:"absolute"};function jo(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}var Fo=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?Ao:o;return function(e,t,n){void 0===n&&(n=a);var o,i,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ao,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},u=[],s=!1,c={state:l,setOptions:function(n){var o="function"==typeof n?n(l.options):n;f(),l.options=Object.assign({},a,l.options,o),l.scrollParents={reference:Fr(e)?Eo(e):e.contextElement?Eo(e.contextElement):[],popper:Eo(t)};var i,s,d=function(e){var t=Lo(e);return Lr.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((i=[].concat(r,l.options.modifiers),s=i.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(s).map((function(e){return s[e]}))));return l.orderedModifiers=d.filter((function(e){return e.enabled})),l.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"==typeof o){var a=o({state:l,name:t,instance:c,options:r}),i=function(){};u.push(a||i)}})),c.update()},forceUpdate:function(){if(!s){var e=l.elements,t=e.reference,n=e.popper;if(jo(t,n)){l.rects={reference:zo(t,to(n),"fixed"===l.options.strategy),popper:qr(n)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach((function(e){return l.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<l.orderedModifiers.length;r++)if(!0!==l.reset){var o=l.orderedModifiers[r],a=o.fn,i=o.options,u=void 0===i?{}:i,f=o.name;"function"==typeof a&&(l=a({state:l,options:u,name:f,instance:c})||l)}else l.reset=!1,r=-1}}},update:(o=function(){return new Promise((function(e){c.forceUpdate(),e(l)}))},function(){return i||(i=new Promise((function(e){Promise.resolve().then((function(){i=void 0,e(o())}))}))),i}),destroy:function(){f(),s=!0}};if(!jo(e,t))return c;function f(){u.forEach((function(e){return e()})),u=[]}return c.setOptions(n).then((function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)})),c}}({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=Oo(t,{elementContext:"reference"}),l=Oo(t,{altBoundary:!0}),u=To(i,r),s=To(l,o,a),c=Po(u),f=Po(s);t.modifiersData[n]={referenceClippingOffsets:u,popperEscapeOffsets:s,isReferenceHidden:c,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":f})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=xo({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},co,po,Ro,Co,No,io]}),Io=function(e){return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}},Uo={name:"applyStyles",enabled:!1},Wo={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:function(e){var t=e.state;return function(){var e=t.elements,n=e.reference,r=e.popper;if("removeAttribute"in n){var o=(n.getAttribute("aria-describedby")||"").split(",").filter((function(e){return e.trim()!==r.id}));o.length?n.setAttribute("aria-describedby",o.join(",")):n.removeAttribute("aria-describedby")}}},fn:function(e){var t,n=e.state.elements,r=n.popper,o=n.reference,a=null==(t=r.getAttribute("role"))?void 0:t.toLowerCase();if(r.id&&"tooltip"===a&&"setAttribute"in o){var i=o.getAttribute("aria-describedby");if(i&&-1!==i.split(",").indexOf(r.id))return;o.setAttribute("aria-describedby",i?i+","+r.id:r.id)}}},Ho=[];function Vo(e,t,n,r){return function(e,t,n,r){if(r&&"boolean"!=typeof r&&!mr){var o=r.once,a=r.capture,i=n;!mr&&o&&(i=n.__once||function e(r){this.removeEventListener(t,e,a),n.call(this,r)},n.__once=i),e.addEventListener(t,i,hr?r:a)}e.addEventListener(t,n,r)}(e,t,n,r),function(){!function(e,t,n,r){var o=r&&"boolean"!=typeof r?r.capture:r;e.removeEventListener(t,n,o),n.__once&&e.removeEventListener(t,n.__once,o)}(e,t,n,r)}}var Bo={},$o={get exports(){return Bo},set exports(e){Bo=e}},Yo={},qo={},Ko={get exports(){return qo},set exports(e){qo=e}},Qo={};
/** @license React v0.20.2
   * scheduler.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
!function(e){var t,n,r,o;if("object"==typeof performance&&"function"==typeof performance.now){var a=performance;e.unstable_now=function(){return a.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}if("undefined"==typeof window||"function"!=typeof MessageChannel){var u=null,s=null,c=function(){if(null!==u)try{var t=e.unstable_now();u(!0,t),u=null}catch(e){throw setTimeout(c,0),e}};t=function(e){null!==u?setTimeout(t,0,e):(u=e,setTimeout(c,0))},n=function(e,t){s=setTimeout(e,t)},r=function(){clearTimeout(s)},e.unstable_shouldYield=function(){return!1},o=e.unstable_forceFrameRate=function(){}}else{var f=window.setTimeout,d=window.clearTimeout;if("undefined"!=typeof console){var p=window.cancelAnimationFrame;"function"!=typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),"function"!=typeof p&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills")}var v=!1,h=null,m=-1,g=5,y=0;e.unstable_shouldYield=function(){return e.unstable_now()>=y},o=function(){},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):g=0<e?Math.floor(1e3/e):5};var b=new MessageChannel,w=b.port2;b.port1.onmessage=function(){if(null!==h){var t=e.unstable_now();y=t+g;try{h(!0,t)?w.postMessage(null):(v=!1,h=null)}catch(e){throw w.postMessage(null),e}}else v=!1},t=function(e){h=e,v||(v=!0,w.postMessage(null))},n=function(t,n){m=f((function(){t(e.unstable_now())}),n)},r=function(){d(m),m=-1}}function k(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,o=e[r];if(!(void 0!==o&&0<D(o,t)))break e;e[r]=t,e[n]=o,n=r}}function E(e){return void 0===(e=e[0])?null:e}function S(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length;r<o;){var a=2*(r+1)-1,i=e[a],l=a+1,u=e[l];if(void 0!==i&&0>D(i,n))void 0!==u&&0>D(u,i)?(e[r]=u,e[l]=n,r=l):(e[r]=i,e[a]=n,r=a);else{if(!(void 0!==u&&0>D(u,n)))break e;e[r]=u,e[l]=n,r=l}}}return t}return null}function D(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var _=[],x=[],O=1,M=null,C=3,T=!1,P=!1,R=!1;function N(e){for(var t=E(x);null!==t;){if(null===t.callback)S(x);else{if(!(t.startTime<=e))break;S(x),t.sortIndex=t.expirationTime,k(_,t)}t=E(x)}}function z(e){if(R=!1,N(e),!P)if(null!==E(_))P=!0,t(L);else{var r=E(x);null!==r&&n(z,r.startTime-e)}}function L(t,o){P=!1,R&&(R=!1,r()),T=!0;var a=C;try{for(N(o),M=E(_);null!==M&&(!(M.expirationTime>o)||t&&!e.unstable_shouldYield());){var i=M.callback;if("function"==typeof i){M.callback=null,C=M.priorityLevel;var l=i(M.expirationTime<=o);o=e.unstable_now(),"function"==typeof l?M.callback=l:M===E(_)&&S(_),N(o)}else S(_);M=E(_)}if(null!==M)var u=!0;else{var s=E(x);null!==s&&n(z,s.startTime-o),u=!1}return u}finally{M=null,C=a,T=!1}}var A=o;e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){P||T||(P=!0,t(L))},e.unstable_getCurrentPriorityLevel=function(){return C},e.unstable_getFirstCallbackNode=function(){return E(_)},e.unstable_next=function(e){switch(C){case 1:case 2:case 3:var t=3;break;default:t=C}var n=C;C=t;try{return e()}finally{C=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=A,e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=C;C=e;try{return t()}finally{C=n}},e.unstable_scheduleCallback=function(o,a,i){var l=e.unstable_now();switch("object"==typeof i&&null!==i?i="number"==typeof(i=i.delay)&&0<i?l+i:l:i=l,o){case 1:var u=-1;break;case 2:u=250;break;case 5:u=**********;break;case 4:u=1e4;break;default:u=5e3}return o={id:O++,callback:a,priorityLevel:o,startTime:i,expirationTime:u=i+u,sortIndex:-1},i>l?(o.sortIndex=i,k(x,o),null===E(_)&&o===E(x)&&(R?r():R=!0,n(z,i-l))):(o.sortIndex=u,k(_,o),P||T||(P=!0,t(L))),o},e.unstable_wrapCallback=function(e){var t=C;return function(){var n=C;C=t;try{return e.apply(this,arguments)}finally{C=n}}}}(Qo),function(e){e.exports=Qo}(Ko);
/** @license React v17.0.2
   * react-dom.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
var Go=O,Xo=N,Jo=qo;function Zo(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!Go)throw Error(Zo(227));var ea=new Set,ta={};function na(e,t){ra(e,t),ra(e+"Capture",t)}function ra(e,t){for(ta[e]=t,e=0;e<t.length;e++)ea.add(t[e])}var oa=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),aa=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ia=Object.prototype.hasOwnProperty,la={},ua={};function sa(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var ca={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){ca[e]=new sa(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];ca[t]=new sa(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){ca[e]=new sa(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){ca[e]=new sa(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){ca[e]=new sa(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){ca[e]=new sa(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){ca[e]=new sa(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){ca[e]=new sa(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){ca[e]=new sa(e,5,!1,e.toLowerCase(),null,!1,!1)}));var fa=/[\-:]([a-z])/g;function da(e){return e[1].toUpperCase()}function pa(e,t,n,r){var o=ca.hasOwnProperty(t)?ca[t]:null;(null!==o?0===o.type:!r&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!ia.call(ua,e)||!ia.call(la,e)&&(aa.test(e)?ua[e]=!0:(la[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(fa,da);ca[t]=new sa(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(fa,da);ca[t]=new sa(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(fa,da);ca[t]=new sa(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){ca[e]=new sa(e,1,!1,e.toLowerCase(),null,!1,!1)})),ca.xlinkHref=new sa("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){ca[e]=new sa(e,1,!1,e.toLowerCase(),null,!0,!0)}));var va=Go.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ha=60103,ma=60106,ga=60107,ya=60108,ba=60114,wa=60109,ka=60110,Ea=60112,Sa=60113,Da=60120,_a=60115,xa=60116,Oa=60121,Ma=60128,Ca=60129,Ta=60130,Pa=60131;if("function"==typeof Symbol&&Symbol.for){var Ra=Symbol.for;ha=Ra("react.element"),ma=Ra("react.portal"),ga=Ra("react.fragment"),ya=Ra("react.strict_mode"),ba=Ra("react.profiler"),wa=Ra("react.provider"),ka=Ra("react.context"),Ea=Ra("react.forward_ref"),Sa=Ra("react.suspense"),Da=Ra("react.suspense_list"),_a=Ra("react.memo"),xa=Ra("react.lazy"),Oa=Ra("react.block"),Ra("react.scope"),Ma=Ra("react.opaque.id"),Ca=Ra("react.debug_trace_mode"),Ta=Ra("react.offscreen"),Pa=Ra("react.legacy_hidden")}var Na,za="function"==typeof Symbol&&Symbol.iterator;function La(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=za&&e[za]||e["@@iterator"])?e:null}function Aa(e){if(void 0===Na)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);Na=t&&t[1]||""}return"\n"+Na+e}var ja=!1;function Fa(e,t){if(!e||ja)return"";ja=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(e){if(e&&r&&"string"==typeof e.stack){for(var o=e.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l])return"\n"+o[i].replace(" at new "," at ")}while(1<=i&&0<=l);break}}}finally{ja=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Aa(e):""}function Ia(e){switch(e.tag){case 5:return Aa(e.type);case 16:return Aa("Lazy");case 13:return Aa("Suspense");case 19:return Aa("SuspenseList");case 0:case 2:case 15:return e=Fa(e.type,!1);case 11:return e=Fa(e.type.render,!1);case 22:return e=Fa(e.type._render,!1);case 1:return e=Fa(e.type,!0);default:return""}}function Ua(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case ga:return"Fragment";case ma:return"Portal";case ba:return"Profiler";case ya:return"StrictMode";case Sa:return"Suspense";case Da:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case ka:return(e.displayName||"Context")+".Consumer";case wa:return(e._context.displayName||"Context")+".Provider";case Ea:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case _a:return Ua(e.type);case Oa:return Ua(e._render);case xa:t=e._payload,e=e._init;try{return Ua(e(t))}catch(e){}}return null}function Wa(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function Ha(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Va(e){e._valueTracker||(e._valueTracker=function(e){var t=Ha(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Ba(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ha(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function $a(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ya(e,t){var n=t.checked;return Xo({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function qa(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=Wa(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Ka(e,t){null!=(t=t.checked)&&pa(e,"checked",t,!1)}function Qa(e,t){Ka(e,t);var n=Wa(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?Xa(e,t.type,n):t.hasOwnProperty("defaultValue")&&Xa(e,t.type,Wa(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Ga(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function Xa(e,t,n){"number"===t&&$a(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}function Ja(e,t){return e=Xo({children:void 0},t),(t=function(e){var t="";return Go.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(t.children))&&(e.children=t),e}function Za(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Wa(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function ei(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(Zo(91));return Xo({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ti(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(Zo(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(Zo(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:Wa(n)}}function ni(e,t){var n=Wa(t.value),r=Wa(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ri(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var oi={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"};function ai(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ii(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ai(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var li,ui,si=(ui=function(e,t){if(e.namespaceURI!==oi.svg||"innerHTML"in e)e.innerHTML=t;else{for((li=li||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ui(e,t)}))}:ui);function ci(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fi={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},di=["Webkit","ms","Moz","O"];function pi(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||fi.hasOwnProperty(e)&&fi[e]?(""+t).trim():t+"px"}function vi(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=pi(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(fi).forEach((function(e){di.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fi[t]=fi[e]}))}));var hi=Xo({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function mi(e,t){if(t){if(hi[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(Zo(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(Zo(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(Zo(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(Zo(62))}}function gi(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}function yi(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var bi=null,wi=null,ki=null;function Ei(e){if(e=Ks(e)){if("function"!=typeof bi)throw Error(Zo(280));var t=e.stateNode;t&&(t=Gs(t),bi(e.stateNode,e.type,t))}}function Si(e){wi?ki?ki.push(e):ki=[e]:wi=e}function Di(){if(wi){var e=wi,t=ki;if(ki=wi=null,Ei(e),t)for(e=0;e<t.length;e++)Ei(t[e])}}function _i(e,t){return e(t)}function xi(e,t,n,r,o){return e(t,n,r,o)}function Oi(){}var Mi=_i,Ci=!1,Ti=!1;function Pi(){null===wi&&null===ki||(Oi(),Di())}function Ri(e,t){var n=e.stateNode;if(null===n)return null;var r=Gs(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(Zo(231,t,typeof n));return n}var Ni=!1;if(oa)try{var zi={};Object.defineProperty(zi,"passive",{get:function(){Ni=!0}}),window.addEventListener("test",zi,zi),window.removeEventListener("test",zi,zi)}catch(ui){Ni=!1}function Li(e,t,n,r,o,a,i,l,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(e){this.onError(e)}}var Ai=!1,ji=null,Fi=!1,Ii=null,Ui={onError:function(e){Ai=!0,ji=e}};function Wi(e,t,n,r,o,a,i,l,u){Ai=!1,ji=null,Li.apply(Ui,arguments)}function Hi(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(1026&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Vi(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Bi(e){if(Hi(e)!==e)throw Error(Zo(188))}function $i(e){if(e=function(e){var t=e.alternate;if(!t){if(null===(t=Hi(e)))throw Error(Zo(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return Bi(o),e;if(a===r)return Bi(o),t;a=a.sibling}throw Error(Zo(188))}if(n.return!==r.return)n=o,r=a;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=a;break}if(l===r){i=!0,r=o,n=a;break}l=l.sibling}if(!i){for(l=a.child;l;){if(l===n){i=!0,n=a,r=o;break}if(l===r){i=!0,r=a,n=o;break}l=l.sibling}if(!i)throw Error(Zo(189))}}if(n.alternate!==r)throw Error(Zo(190))}if(3!==n.tag)throw Error(Zo(188));return n.stateNode.current===n?e:t}(e),!e)return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function Yi(e,t){for(var n=e.alternate;null!==t;){if(t===e||t===n)return!0;t=t.return}return!1}var qi,Ki,Qi,Gi,Xi=!1,Ji=[],Zi=null,el=null,tl=null,nl=new Map,rl=new Map,ol=[],al="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function il(e,t,n,r,o){return{blockedOn:e,domEventName:t,eventSystemFlags:16|n,nativeEvent:o,targetContainers:[r]}}function ll(e,t){switch(e){case"focusin":case"focusout":Zi=null;break;case"dragenter":case"dragleave":el=null;break;case"mouseover":case"mouseout":tl=null;break;case"pointerover":case"pointerout":nl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":rl.delete(t.pointerId)}}function ul(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e=il(t,n,r,o,a),null!==t&&(null!==(t=Ks(t))&&Ki(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function sl(e){var t=qs(e.target);if(null!==t){var n=Hi(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Vi(n)))return e.blockedOn=t,void Gi(e.lanePriority,(function(){Jo.unstable_runWithPriority(e.priority,(function(){Qi(n)}))}))}else if(3===t&&n.stateNode.hydrate)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function cl(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=$l(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=Ks(n))&&Ki(t),e.blockedOn=n,!1;t.shift()}return!0}function fl(e,t,n){cl(e)&&n.delete(t)}function dl(){for(Xi=!1;0<Ji.length;){var e=Ji[0];if(null!==e.blockedOn){null!==(e=Ks(e.blockedOn))&&qi(e);break}for(var t=e.targetContainers;0<t.length;){var n=$l(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n){e.blockedOn=n;break}t.shift()}null===e.blockedOn&&Ji.shift()}null!==Zi&&cl(Zi)&&(Zi=null),null!==el&&cl(el)&&(el=null),null!==tl&&cl(tl)&&(tl=null),nl.forEach(fl),rl.forEach(fl)}function pl(e,t){e.blockedOn===t&&(e.blockedOn=null,Xi||(Xi=!0,Jo.unstable_scheduleCallback(Jo.unstable_NormalPriority,dl)))}function vl(e){function t(t){return pl(t,e)}if(0<Ji.length){pl(Ji[0],e);for(var n=1;n<Ji.length;n++){var r=Ji[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Zi&&pl(Zi,e),null!==el&&pl(el,e),null!==tl&&pl(tl,e),nl.forEach(t),rl.forEach(t),n=0;n<ol.length;n++)(r=ol[n]).blockedOn===e&&(r.blockedOn=null);for(;0<ol.length&&null===(n=ol[0]).blockedOn;)sl(n),null===n.blockedOn&&ol.shift()}function hl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ml={animationend:hl("Animation","AnimationEnd"),animationiteration:hl("Animation","AnimationIteration"),animationstart:hl("Animation","AnimationStart"),transitionend:hl("Transition","TransitionEnd")},gl={},yl={};function bl(e){if(gl[e])return gl[e];if(!ml[e])return e;var t,n=ml[e];for(t in n)if(n.hasOwnProperty(t)&&t in yl)return gl[e]=n[t];return e}oa&&(yl=document.createElement("div").style,"AnimationEvent"in window||(delete ml.animationend.animation,delete ml.animationiteration.animation,delete ml.animationstart.animation),"TransitionEvent"in window||delete ml.transitionend.transition);var wl=bl("animationend"),kl=bl("animationiteration"),El=bl("animationstart"),Sl=bl("transitionend"),Dl=new Map,_l=new Map,xl=["abort","abort",wl,"animationEnd",kl,"animationIteration",El,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Sl,"transitionEnd","waiting","waiting"];function Ol(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],o=e[n+1];o="on"+(o[0].toUpperCase()+o.slice(1)),_l.set(r,t),Dl.set(r,o),na(o,[r])}}(0,Jo.unstable_now)();var Ml=8;function Cl(e){if(1&e)return Ml=15,1;if(2&e)return Ml=14,2;if(4&e)return Ml=13,4;var t=24&e;return 0!==t?(Ml=12,t):32&e?(Ml=11,32):0!==(t=192&e)?(Ml=10,t):256&e?(Ml=9,256):0!==(t=3584&e)?(Ml=8,t):4096&e?(Ml=7,4096):0!==(t=4186112&e)?(Ml=6,t):0!==(t=62914560&e)?(Ml=5,t):67108864&e?(Ml=4,67108864):134217728&e?(Ml=3,134217728):0!==(t=805306368&e)?(Ml=2,t):1073741824&e?(Ml=1,1073741824):(Ml=8,e)}function Tl(e,t){var n=e.pendingLanes;if(0===n)return Ml=0;var r=0,o=0,a=e.expiredLanes,i=e.suspendedLanes,l=e.pingedLanes;if(0!==a)r=a,o=Ml=15;else if(0!==(a=134217727&n)){var u=a&~i;0!==u?(r=Cl(u),o=Ml):0!==(l&=a)&&(r=Cl(l),o=Ml)}else 0!==(a=n&~i)?(r=Cl(a),o=Ml):0!==l&&(r=Cl(l),o=Ml);if(0===r)return 0;if(r=n&((0>(r=31-Al(r))?0:1<<r)<<1)-1,0!==t&&t!==r&&!(t&i)){if(Cl(t),o<=Ml)return t;Ml=o}if(0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-Al(t)),r|=e[n],t&=~o;return r}function Pl(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function Rl(e,t){switch(e){case 15:return 1;case 14:return 2;case 12:return 0===(e=Nl(24&~t))?Rl(10,t):e;case 10:return 0===(e=Nl(192&~t))?Rl(8,t):e;case 8:return 0===(e=Nl(3584&~t))&&(0===(e=Nl(4186112&~t))&&(e=512)),e;case 2:return 0===(t=Nl(805306368&~t))&&(t=268435456),t}throw Error(Zo(358,e))}function Nl(e){return e&-e}function zl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ll(e,t,n){e.pendingLanes|=t;var r=t-1;e.suspendedLanes&=r,e.pingedLanes&=r,(e=e.eventTimes)[t=31-Al(t)]=n}var Al=Math.clz32?Math.clz32:function(e){return 0===e?32:31-(jl(e)/Fl|0)|0},jl=Math.log,Fl=Math.LN2;var Il=Jo.unstable_UserBlockingPriority,Ul=Jo.unstable_runWithPriority,Wl=!0;function Hl(e,t,n,r){Ci||Oi();var o=Bl,a=Ci;Ci=!0;try{xi(o,e,t,n,r)}finally{(Ci=a)||Pi()}}function Vl(e,t,n,r){Ul(Il,Bl.bind(null,e,t,n,r))}function Bl(e,t,n,r){var o;if(Wl)if((o=!(4&t))&&0<Ji.length&&-1<al.indexOf(e))e=il(null,e,t,n,r),Ji.push(e);else{var a=$l(e,t,n,r);if(null===a)o&&ll(e,r);else{if(o){if(-1<al.indexOf(e))return e=il(a,e,t,n,r),void Ji.push(e);if(function(e,t,n,r,o){switch(t){case"focusin":return Zi=ul(Zi,e,t,n,r,o),!0;case"dragenter":return el=ul(el,e,t,n,r,o),!0;case"mouseover":return tl=ul(tl,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return nl.set(a,ul(nl.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,rl.set(a,ul(rl.get(a)||null,e,t,n,r,o)),!0}return!1}(a,e,t,n,r))return;ll(e,r)}xs(e,t,r,null,n)}}}function $l(e,t,n,r){var o=yi(r);if(null!==(o=qs(o))){var a=Hi(o);if(null===a)o=null;else{var i=a.tag;if(13===i){if(null!==(o=Vi(a)))return o;o=null}else if(3===i){if(a.stateNode.hydrate)return 3===a.tag?a.stateNode.containerInfo:null;o=null}else a!==o&&(o=null)}}return xs(e,t,r,o,n),null}var Yl=null,ql=null,Kl=null;function Ql(){if(Kl)return Kl;var e,t,n=ql,r=n.length,o="value"in Yl?Yl.value:Yl.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Kl=o.slice(e,1<t?1-t:void 0)}function Gl(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Xl(){return!0}function Jl(){return!1}function Zl(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?Xl:Jl,this.isPropagationStopped=Jl,this}return Xo(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Xl)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Xl)},persist:function(){},isPersistent:Xl}),t}var eu,tu,nu,ru={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ou=Zl(ru),au=Xo({},ru,{view:0,detail:0}),iu=Zl(au),lu=Xo({},au,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bu,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==nu&&(nu&&"mousemove"===e.type?(eu=e.screenX-nu.screenX,tu=e.screenY-nu.screenY):tu=eu=0,nu=e),eu)},movementY:function(e){return"movementY"in e?e.movementY:tu}}),uu=Zl(lu),su=Zl(Xo({},lu,{dataTransfer:0})),cu=Zl(Xo({},au,{relatedTarget:0})),fu=Zl(Xo({},ru,{animationName:0,elapsedTime:0,pseudoElement:0})),du=Xo({},ru,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),pu=Zl(du),vu=Zl(Xo({},ru,{data:0})),hu={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mu={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gu={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yu(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=gu[e])&&!!t[e]}function bu(){return yu}var wu=Xo({},au,{key:function(e){if(e.key){var t=hu[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Gl(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?mu[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bu,charCode:function(e){return"keypress"===e.type?Gl(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Gl(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),ku=Zl(wu),Eu=Zl(Xo({},lu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Su=Zl(Xo({},au,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bu})),Du=Zl(Xo({},ru,{propertyName:0,elapsedTime:0,pseudoElement:0})),_u=Xo({},lu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xu=Zl(_u),Ou=[9,13,27,32],Mu=oa&&"CompositionEvent"in window,Cu=null;oa&&"documentMode"in document&&(Cu=document.documentMode);var Tu=oa&&"TextEvent"in window&&!Cu,Pu=oa&&(!Mu||Cu&&8<Cu&&11>=Cu),Ru=String.fromCharCode(32),Nu=!1;function zu(e,t){switch(e){case"keyup":return-1!==Ou.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Lu(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Au=!1;var ju={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!ju[e.type]:"textarea"===t}function Iu(e,t,n,r){Si(r),0<(t=Ms(t,"onChange")).length&&(n=new ou("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Uu=null,Wu=null;function Hu(e){ws(e,0)}function Vu(e){if(Ba(Qs(e)))return e}function Bu(e,t){if("change"===e)return t}var $u=!1;if(oa){var Yu;if(oa){var qu="oninput"in document;if(!qu){var Ku=document.createElement("div");Ku.setAttribute("oninput","return;"),qu="function"==typeof Ku.oninput}Yu=qu}else Yu=!1;$u=Yu&&(!document.documentMode||9<document.documentMode)}function Qu(){Uu&&(Uu.detachEvent("onpropertychange",Gu),Wu=Uu=null)}function Gu(e){if("value"===e.propertyName&&Vu(Wu)){var t=[];if(Iu(t,Wu,e,yi(e)),e=Hu,Ci)e(t);else{Ci=!0;try{_i(e,t)}finally{Ci=!1,Pi()}}}}function Xu(e,t,n){"focusin"===e?(Qu(),Wu=n,(Uu=t).attachEvent("onpropertychange",Gu)):"focusout"===e&&Qu()}function Ju(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Vu(Wu)}function Zu(e,t){if("click"===e)return Vu(t)}function es(e,t){if("input"===e||"change"===e)return Vu(t)}var ts="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},ns=Object.prototype.hasOwnProperty;function rs(e,t){if(ts(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!ns.call(t,n[r])||!ts(e[n[r]],t[n[r]]))return!1;return!0}function os(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function as(e,t){var n,r=os(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=os(r)}}function is(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?is(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function ls(){for(var e=window,t=$a();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=$a((e=t.contentWindow).document)}return t}function us(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var ss=oa&&"documentMode"in document&&11>=document.documentMode,cs=null,fs=null,ds=null,ps=!1;function vs(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ps||null==cs||cs!==$a(r)||("selectionStart"in(r=cs)&&us(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ds&&rs(ds,r)||(ds=r,0<(r=Ms(fs,"onSelect")).length&&(t=new ou("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=cs)))}Ol("cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focusin focus focusout blur input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),Ol("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),Ol(xl,2);for(var hs="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),ms=0;ms<hs.length;ms++)_l.set(hs[ms],0);ra("onMouseEnter",["mouseout","mouseover"]),ra("onMouseLeave",["mouseout","mouseover"]),ra("onPointerEnter",["pointerout","pointerover"]),ra("onPointerLeave",["pointerout","pointerover"]),na("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),na("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),na("onBeforeInput",["compositionend","keypress","textInput","paste"]),na("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),na("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),na("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ys=new Set("cancel close invalid load scroll toggle".split(" ").concat(gs));function bs(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,a,i,l,u){if(Wi.apply(this,arguments),Ai){if(!Ai)throw Error(Zo(198));var s=ji;Ai=!1,ji=null,Fi||(Fi=!0,Ii=s)}}(r,t,void 0,e),e.currentTarget=null}function ws(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],u=l.instance,s=l.currentTarget;if(l=l.listener,u!==a&&o.isPropagationStopped())break e;bs(o,l,s),a=u}else for(i=0;i<r.length;i++){if(u=(l=r[i]).instance,s=l.currentTarget,l=l.listener,u!==a&&o.isPropagationStopped())break e;bs(o,l,s),a=u}}}if(Fi)throw e=Ii,Fi=!1,Ii=null,e}function ks(e,t){var n=Xs(t),r=e+"__bubble";n.has(r)||(_s(t,e,2,!1),n.add(r))}var Es="_reactListening"+Math.random().toString(36).slice(2);function Ss(e){e[Es]||(e[Es]=!0,ea.forEach((function(t){ys.has(t)||Ds(t,!1,e,null),Ds(t,!0,e,null)})))}function Ds(e,t,n,r){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:0,a=n;if("selectionchange"===e&&9!==n.nodeType&&(a=n.ownerDocument),null!==r&&!t&&ys.has(e)){if("scroll"!==e)return;o|=2,a=r}var i=Xs(a),l=e+"__"+(t?"capture":"bubble");i.has(l)||(t&&(o|=4),_s(a,e,o,t),i.add(l))}function _s(e,t,n,r){var o=_l.get(t);switch(void 0===o?2:o){case 0:o=Hl;break;case 1:o=Vl;break;default:o=Bl}n=o.bind(null,t,n,e),o=void 0,!Ni||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function xs(e,t,n,r,o){var a=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&((u=i.stateNode.containerInfo)===o||8===u.nodeType&&u.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=qs(l)))return;if(5===(u=i.tag)||6===u){r=a=i;continue e}l=l.parentNode}}r=r.return}!function(e,t,n){if(Ti)return e(t,n);Ti=!0;try{return Mi(e,t,n)}finally{Ti=!1,Pi()}}((function(){var r=a,o=yi(n),i=[];e:{var l=Dl.get(e);if(void 0!==l){var u=ou,s=e;switch(e){case"keypress":if(0===Gl(n))break e;case"keydown":case"keyup":u=ku;break;case"focusin":s="focus",u=cu;break;case"focusout":s="blur",u=cu;break;case"beforeblur":case"afterblur":u=cu;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=uu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=su;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=Su;break;case wl:case kl:case El:u=fu;break;case Sl:u=Du;break;case"scroll":u=iu;break;case"wheel":u=xu;break;case"copy":case"cut":case"paste":u=pu;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=Eu}var c=!!(4&t),f=!c&&"scroll"===e,d=c?null!==l?l+"Capture":null:l;c=[];for(var p,v=r;null!==v;){var h=(p=v).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==d&&(null!=(h=Ri(v,d))&&c.push(Os(v,h,p)))),f)break;v=v.return}0<c.length&&(l=new u(l,s,null,n,o),i.push({event:l,listeners:c}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||16&t||!(s=n.relatedTarget||n.fromElement)||!qs(s)&&!s[$s])&&(u||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,u?(u=r,null!==(s=(s=n.relatedTarget||n.toElement)?qs(s):null)&&(s!==(f=Hi(s))||5!==s.tag&&6!==s.tag)&&(s=null)):(u=null,s=r),u!==s)){if(c=uu,h="onMouseLeave",d="onMouseEnter",v="mouse","pointerout"!==e&&"pointerover"!==e||(c=Eu,h="onPointerLeave",d="onPointerEnter",v="pointer"),f=null==u?l:Qs(u),p=null==s?l:Qs(s),(l=new c(h,v+"leave",u,n,o)).target=f,l.relatedTarget=p,h=null,qs(o)===r&&((c=new c(d,v+"enter",s,n,o)).target=p,c.relatedTarget=f,h=c),f=h,u&&s)e:{for(d=s,v=0,p=c=u;p;p=Cs(p))v++;for(p=0,h=d;h;h=Cs(h))p++;for(;0<v-p;)c=Cs(c),v--;for(;0<p-v;)d=Cs(d),p--;for(;v--;){if(c===d||null!==d&&c===d.alternate)break e;c=Cs(c),d=Cs(d)}c=null}else c=null;null!==u&&Ts(i,l,u,c,!1),null!==s&&null!==f&&Ts(i,f,s,c,!0)}if("select"===(u=(l=r?Qs(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===u&&"file"===l.type)var m=Bu;else if(Fu(l))if($u)m=es;else{m=Ju;var g=Xu}else(u=l.nodeName)&&"input"===u.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(m=Zu);switch(m&&(m=m(e,r))?Iu(i,m,n,o):(g&&g(e,l,r),"focusout"===e&&(g=l._wrapperState)&&g.controlled&&"number"===l.type&&Xa(l,"number",l.value)),g=r?Qs(r):window,e){case"focusin":(Fu(g)||"true"===g.contentEditable)&&(cs=g,fs=r,ds=null);break;case"focusout":ds=fs=cs=null;break;case"mousedown":ps=!0;break;case"contextmenu":case"mouseup":case"dragend":ps=!1,vs(i,n,o);break;case"selectionchange":if(ss)break;case"keydown":case"keyup":vs(i,n,o)}var y;if(Mu)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Au?zu(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Pu&&"ko"!==n.locale&&(Au||"onCompositionStart"!==b?"onCompositionEnd"===b&&Au&&(y=Ql()):(ql="value"in(Yl=o)?Yl.value:Yl.textContent,Au=!0)),0<(g=Ms(r,b)).length&&(b=new vu(b,e,null,n,o),i.push({event:b,listeners:g}),y?b.data=y:null!==(y=Lu(n))&&(b.data=y))),(y=Tu?function(e,t){switch(e){case"compositionend":return Lu(t);case"keypress":return 32!==t.which?null:(Nu=!0,Ru);case"textInput":return(e=t.data)===Ru&&Nu?null:e;default:return null}}(e,n):function(e,t){if(Au)return"compositionend"===e||!Mu&&zu(e,t)?(e=Ql(),Kl=ql=Yl=null,Au=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pu&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Ms(r,"onBeforeInput")).length&&(o=new vu("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}ws(i,t)}))}function Os(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ms(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Ri(e,n))&&r.unshift(Os(e,a,o)),null!=(a=Ri(e,t))&&r.push(Os(e,a,o))),e=e.return}return r}function Cs(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Ts(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,u=l.alternate,s=l.stateNode;if(null!==u&&u===r)break;5===l.tag&&null!==s&&(l=s,o?null!=(u=Ri(n,a))&&i.unshift(Os(n,u,l)):o||null!=(u=Ri(n,a))&&i.push(Os(n,u,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}function Ps(){}var Rs=null,Ns=null;function zs(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function Ls(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var As="function"==typeof setTimeout?setTimeout:void 0,js="function"==typeof clearTimeout?clearTimeout:void 0;function Fs(e){1===e.nodeType?e.textContent="":9===e.nodeType&&(null!=(e=e.body)&&(e.textContent=""))}function Is(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function Us(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var Ws=0;var Hs=Math.random().toString(36).slice(2),Vs="__reactFiber$"+Hs,Bs="__reactProps$"+Hs,$s="__reactContainer$"+Hs,Ys="__reactEvents$"+Hs;function qs(e){var t=e[Vs];if(t)return t;for(var n=e.parentNode;n;){if(t=n[$s]||n[Vs]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=Us(e);null!==e;){if(n=e[Vs])return n;e=Us(e)}return t}n=(e=n).parentNode}return null}function Ks(e){return!(e=e[Vs]||e[$s])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Qs(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(Zo(33))}function Gs(e){return e[Bs]||null}function Xs(e){var t=e[Ys];return void 0===t&&(t=e[Ys]=new Set),t}var Js=[],Zs=-1;function ec(e){return{current:e}}function tc(e){0>Zs||(e.current=Js[Zs],Js[Zs]=null,Zs--)}function nc(e,t){Zs++,Js[Zs]=e.current,e.current=t}var rc={},oc=ec(rc),ac=ec(!1),ic=rc;function lc(e,t){var n=e.type.contextTypes;if(!n)return rc;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function uc(e){return null!=(e=e.childContextTypes)}function sc(){tc(ac),tc(oc)}function cc(e,t,n){if(oc.current!==rc)throw Error(Zo(168));nc(oc,t),nc(ac,n)}function fc(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in e))throw Error(Zo(108,Ua(t)||"Unknown",o));return Xo({},n,r)}function dc(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||rc,ic=oc.current,nc(oc,e),nc(ac,ac.current),!0}function pc(e,t,n){var r=e.stateNode;if(!r)throw Error(Zo(169));n?(e=fc(e,t,ic),r.__reactInternalMemoizedMergedChildContext=e,tc(ac),tc(oc),nc(oc,e)):tc(ac),nc(ac,n)}var vc=null,hc=null,mc=Jo.unstable_runWithPriority,gc=Jo.unstable_scheduleCallback,yc=Jo.unstable_cancelCallback,bc=Jo.unstable_shouldYield,wc=Jo.unstable_requestPaint,kc=Jo.unstable_now,Ec=Jo.unstable_getCurrentPriorityLevel,Sc=Jo.unstable_ImmediatePriority,Dc=Jo.unstable_UserBlockingPriority,_c=Jo.unstable_NormalPriority,xc=Jo.unstable_LowPriority,Oc=Jo.unstable_IdlePriority,Mc={},Cc=void 0!==wc?wc:function(){},Tc=null,Pc=null,Rc=!1,Nc=kc(),zc=1e4>Nc?kc:function(){return kc()-Nc};function Lc(){switch(Ec()){case Sc:return 99;case Dc:return 98;case _c:return 97;case xc:return 96;case Oc:return 95;default:throw Error(Zo(332))}}function Ac(e){switch(e){case 99:return Sc;case 98:return Dc;case 97:return _c;case 96:return xc;case 95:return Oc;default:throw Error(Zo(332))}}function jc(e,t){return e=Ac(e),mc(e,t)}function Fc(e,t,n){return e=Ac(e),gc(e,t,n)}function Ic(){if(null!==Pc){var e=Pc;Pc=null,yc(e)}Uc()}function Uc(){if(!Rc&&null!==Tc){Rc=!0;var e=0;try{var t=Tc;jc(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),Tc=null}catch(t){throw null!==Tc&&(Tc=Tc.slice(e+1)),gc(Sc,Ic),t}finally{Rc=!1}}}var Wc=va.ReactCurrentBatchConfig;function Hc(e,t){if(e&&e.defaultProps){for(var n in t=Xo({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var Vc=ec(null),Bc=null,$c=null,Yc=null;function qc(){Yc=$c=Bc=null}function Kc(e){var t=Vc.current;tc(Vc),e.type._context._currentValue=t}function Qc(e,t){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)===t){if(null===n||(n.childLanes&t)===t)break;n.childLanes|=t}else e.childLanes|=t,null!==n&&(n.childLanes|=t);e=e.return}}function Gc(e,t){Bc=e,Yc=$c=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(!!(e.lanes&t)&&(Md=!0),e.firstContext=null)}function Xc(e,t){if(Yc!==e&&!1!==t&&0!==t)if("number"==typeof t&&**********!==t||(Yc=e,t=**********),t={context:e,observedBits:t,next:null},null===$c){if(null===Bc)throw Error(Zo(308));$c=t,Bc.dependencies={lanes:0,firstContext:t,responders:null}}else $c=$c.next=t;return e._currentValue}var Jc=!1;function Zc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null},effects:null}}function ef(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function tf(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function nf(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function rf(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function of(e,t,n,r){var o=e.updateQueue;Jc=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var u=l,s=u.next;u.next=null,null===i?a=s:i.next=s,i=u;var c=e.alternate;if(null!==c){var f=(c=c.updateQueue).lastBaseUpdate;f!==i&&(null===f?c.firstBaseUpdate=s:f.next=s,c.lastBaseUpdate=u)}}if(null!==a){for(f=o.baseState,i=0,c=s=u=null;;){l=a.lane;var d=a.eventTime;if((r&l)===l){null!==c&&(c=c.next={eventTime:d,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var p=e,v=a;switch(l=t,d=n,v.tag){case 1:if("function"==typeof(p=v.payload)){f=p.call(d,f,l);break e}f=p;break e;case 3:p.flags=-4097&p.flags|64;case 0:if(null==(l="function"==typeof(p=v.payload)?p.call(d,f,l):p))break e;f=Xo({},f,l);break e;case 2:Jc=!0}}null!==a.callback&&(e.flags|=32,null===(l=o.effects)?o.effects=[a]:l.push(a))}else d={eventTime:d,lane:l,tag:a.tag,payload:a.payload,callback:a.callback,next:null},null===c?(s=c=d,u=f):c=c.next=d,i|=l;if(null===(a=a.next)){if(null===(l=o.shared.pending))break;a=l.next,l.next=null,o.lastBaseUpdate=l,o.shared.pending=null}}null===c&&(u=f),o.baseState=u,o.firstBaseUpdate=s,o.lastBaseUpdate=c,Np|=i,e.lanes=i,e.memoizedState=f}}function af(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!=typeof o)throw Error(Zo(191,o));o.call(r)}}}var lf=(new Go.Component).refs;function uf(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:Xo({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var sf={isMounted:function(e){return!!(e=e._reactInternals)&&Hi(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ov(),o=av(e),a=tf(r,o);a.payload=t,null!=n&&(a.callback=n),nf(e,a),iv(e,o,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ov(),o=av(e),a=tf(r,o);a.tag=1,a.payload=t,null!=n&&(a.callback=n),nf(e,a),iv(e,o,r)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ov(),r=av(e),o=tf(n,r);o.tag=2,null!=t&&(o.callback=t),nf(e,o),iv(e,r,n)}};function cf(e,t,n,r,o,a,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!rs(n,r)||!rs(o,a))}function ff(e,t,n){var r=!1,o=rc,a=t.contextType;return"object"==typeof a&&null!==a?a=Xc(a):(o=uc(t)?ic:oc.current,a=(r=null!=(r=t.contextTypes))?lc(e,o):rc),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=sf,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function df(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&sf.enqueueReplaceState(t,t.state,null)}function pf(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=lf,Zc(e);var a=t.contextType;"object"==typeof a&&null!==a?o.context=Xc(a):(a=uc(t)?ic:oc.current,o.context=lc(e,a)),of(e,n,o,r),o.state=e.memoizedState,"function"==typeof(a=t.getDerivedStateFromProps)&&(uf(e,t,a,n),o.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof o.getSnapshotBeforeUpdate||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(t=o.state,"function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&sf.enqueueReplaceState(o,o.state,null),of(e,n,o,r),o.state=e.memoizedState),"function"==typeof o.componentDidMount&&(e.flags|=4)}var vf=Array.isArray;function hf(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(Zo(309));var r=n.stateNode}if(!r)throw Error(Zo(147,e));var o=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=r.refs;t===lf&&(t=r.refs={}),null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!=typeof e)throw Error(Zo(284));if(!n._owner)throw Error(Zo(290,e))}return e}function mf(e,t){if("textarea"!==e.type)throw Error(Zo(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t))}function gf(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.flags=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Fv(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags=2,n):r:(t.flags=2,n):n}function i(t){return e&&null===t.alternate&&(t.flags=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Hv(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){return null!==t&&t.elementType===n.type?((r=o(t,n.props)).ref=hf(e,t,n),r.return=e,r):((r=Iv(n.type,n.key,n.props,null,e.mode,r)).ref=hf(e,t,n),r.return=e,r)}function s(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vv(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function c(e,t,n,r,a){return null===t||7!==t.tag?((t=Uv(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t||"number"==typeof t)return(t=Hv(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case ha:return(n=Iv(t.type,t.key,t.props,null,e.mode,n)).ref=hf(e,null,t),n.return=e,n;case ma:return(t=Vv(t,e.mode,n)).return=e,t}if(vf(t)||La(t))return(t=Uv(t,e.mode,n,null)).return=e,t;mf(e,t)}return null}function d(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n||"number"==typeof n)return null!==o?null:l(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case ha:return n.key===o?n.type===ga?c(e,t,n.props.children,r,o):u(e,t,n,r):null;case ma:return n.key===o?s(e,t,n,r):null}if(vf(n)||La(n))return null!==o?null:c(e,t,n,r,null);mf(e,n)}return null}function p(e,t,n,r,o){if("string"==typeof r||"number"==typeof r)return l(t,e=e.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case ha:return e=e.get(null===r.key?n:r.key)||null,r.type===ga?c(t,e,r.props.children,o,r.key):u(t,e,r,o);case ma:return s(t,e=e.get(null===r.key?n:r.key)||null,r,o)}if(vf(r)||La(r))return c(t,e=e.get(n)||null,r,o,null);mf(t,r)}return null}function v(o,i,l,u){for(var s=null,c=null,v=i,h=i=0,m=null;null!==v&&h<l.length;h++){v.index>h?(m=v,v=null):m=v.sibling;var g=d(o,v,l[h],u);if(null===g){null===v&&(v=m);break}e&&v&&null===g.alternate&&t(o,v),i=a(g,i,h),null===c?s=g:c.sibling=g,c=g,v=m}if(h===l.length)return n(o,v),s;if(null===v){for(;h<l.length;h++)null!==(v=f(o,l[h],u))&&(i=a(v,i,h),null===c?s=v:c.sibling=v,c=v);return s}for(v=r(o,v);h<l.length;h++)null!==(m=p(v,o,h,l[h],u))&&(e&&null!==m.alternate&&v.delete(null===m.key?h:m.key),i=a(m,i,h),null===c?s=m:c.sibling=m,c=m);return e&&v.forEach((function(e){return t(o,e)})),s}function h(o,i,l,u){var s=La(l);if("function"!=typeof s)throw Error(Zo(150));if(null==(l=s.call(l)))throw Error(Zo(151));for(var c=s=null,v=i,h=i=0,m=null,g=l.next();null!==v&&!g.done;h++,g=l.next()){v.index>h?(m=v,v=null):m=v.sibling;var y=d(o,v,g.value,u);if(null===y){null===v&&(v=m);break}e&&v&&null===y.alternate&&t(o,v),i=a(y,i,h),null===c?s=y:c.sibling=y,c=y,v=m}if(g.done)return n(o,v),s;if(null===v){for(;!g.done;h++,g=l.next())null!==(g=f(o,g.value,u))&&(i=a(g,i,h),null===c?s=g:c.sibling=g,c=g);return s}for(v=r(o,v);!g.done;h++,g=l.next())null!==(g=p(v,o,h,g.value,u))&&(e&&null!==g.alternate&&v.delete(null===g.key?h:g.key),i=a(g,i,h),null===c?s=g:c.sibling=g,c=g);return e&&v.forEach((function(e){return t(o,e)})),s}return function(e,r,a,l){var u="object"==typeof a&&null!==a&&a.type===ga&&null===a.key;u&&(a=a.props.children);var s="object"==typeof a&&null!==a;if(s)switch(a.$$typeof){case ha:e:{for(s=a.key,u=r;null!==u;){if(u.key===s){if(7===u.tag){if(a.type===ga){n(e,u.sibling),(r=o(u,a.props.children)).return=e,e=r;break e}}else if(u.elementType===a.type){n(e,u.sibling),(r=o(u,a.props)).ref=hf(e,u,a),r.return=e,e=r;break e}n(e,u);break}t(e,u),u=u.sibling}a.type===ga?((r=Uv(a.props.children,e.mode,l,a.key)).return=e,e=r):((l=Iv(a.type,a.key,a.props,null,e.mode,l)).ref=hf(e,r,a),l.return=e,e=l)}return i(e);case ma:e:{for(u=a.key;null!==r;){if(r.key===u){if(4===r.tag&&r.stateNode.containerInfo===a.containerInfo&&r.stateNode.implementation===a.implementation){n(e,r.sibling),(r=o(r,a.children||[])).return=e,e=r;break e}n(e,r);break}t(e,r),r=r.sibling}(r=Vv(a,e.mode,l)).return=e,e=r}return i(e)}if("string"==typeof a||"number"==typeof a)return a=""+a,null!==r&&6===r.tag?(n(e,r.sibling),(r=o(r,a)).return=e,e=r):(n(e,r),(r=Hv(a,e.mode,l)).return=e,e=r),i(e);if(vf(a))return v(e,r,a,l);if(La(a))return h(e,r,a,l);if(s&&mf(e,a),void 0===a&&!u)switch(e.tag){case 1:case 22:case 0:case 11:case 15:throw Error(Zo(152,Ua(e.type)||"Component"))}return n(e,r)}}var yf=gf(!0),bf=gf(!1),wf={},kf=ec(wf),Ef=ec(wf),Sf=ec(wf);function Df(e){if(e===wf)throw Error(Zo(174));return e}function _f(e,t){switch(nc(Sf,t),nc(Ef,e),nc(kf,wf),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ii(null,"");break;default:t=ii(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}tc(kf),nc(kf,t)}function xf(){tc(kf),tc(Ef),tc(Sf)}function Of(e){Df(Sf.current);var t=Df(kf.current),n=ii(t,e.type);t!==n&&(nc(Ef,e),nc(kf,n))}function Mf(e){Ef.current===e&&(tc(kf),tc(Ef))}var Cf=ec(0);function Tf(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(64&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Pf=null,Rf=null,Nf=!1;function zf(e,t){var n=Av(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.flags=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function Lf(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);default:return!1}}function Af(e){if(Nf){var t=Rf;if(t){var n=t;if(!Lf(e,t)){if(!(t=Is(n.nextSibling))||!Lf(e,t))return e.flags=-1025&e.flags|2,Nf=!1,void(Pf=e);zf(Pf,n)}Pf=e,Rf=Is(t.firstChild)}else e.flags=-1025&e.flags|2,Nf=!1,Pf=e}}function jf(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;Pf=e}function Ff(e){if(e!==Pf)return!1;if(!Nf)return jf(e),Nf=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!Ls(t,e.memoizedProps))for(t=Rf;t;)zf(e,t),t=Is(t.nextSibling);if(jf(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(Zo(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){Rf=Is(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}Rf=null}}else Rf=Pf?Is(e.stateNode.nextSibling):null;return!0}function If(){Rf=Pf=null,Nf=!1}var Uf=[];function Wf(){for(var e=0;e<Uf.length;e++)Uf[e]._workInProgressVersionPrimary=null;Uf.length=0}var Hf=va.ReactCurrentDispatcher,Vf=va.ReactCurrentBatchConfig,Bf=0,$f=null,Yf=null,qf=null,Kf=!1,Qf=!1;function Gf(){throw Error(Zo(321))}function Xf(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ts(e[n],t[n]))return!1;return!0}function Jf(e,t,n,r,o,a){if(Bf=a,$f=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Hf.current=null===e||null===e.memoizedState?Dd:_d,e=n(r,o),Qf){a=0;do{if(Qf=!1,!(25>a))throw Error(Zo(301));a+=1,qf=Yf=null,t.updateQueue=null,Hf.current=xd,e=n(r,o)}while(Qf)}if(Hf.current=Sd,t=null!==Yf&&null!==Yf.next,Bf=0,qf=Yf=$f=null,Kf=!1,t)throw Error(Zo(300));return e}function Zf(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===qf?$f.memoizedState=qf=e:qf=qf.next=e,qf}function ed(){if(null===Yf){var e=$f.alternate;e=null!==e?e.memoizedState:null}else e=Yf.next;var t=null===qf?$f.memoizedState:qf.next;if(null!==t)qf=t,Yf=e;else{if(null===e)throw Error(Zo(310));e={memoizedState:(Yf=e).memoizedState,baseState:Yf.baseState,baseQueue:Yf.baseQueue,queue:Yf.queue,next:null},null===qf?$f.memoizedState=qf=e:qf=qf.next=e}return qf}function td(e,t){return"function"==typeof t?t(e):t}function nd(e){var t=ed(),n=t.queue;if(null===n)throw Error(Zo(311));n.lastRenderedReducer=e;var r=Yf,o=r.baseQueue,a=n.pending;if(null!==a){if(null!==o){var i=o.next;o.next=a.next,a.next=i}r.baseQueue=o=a,n.pending=null}if(null!==o){o=o.next,r=r.baseState;var l=i=a=null,u=o;do{var s=u.lane;if((Bf&s)===s)null!==l&&(l=l.next={lane:0,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null}),r=u.eagerReducer===e?u.eagerState:e(r,u.action);else{var c={lane:s,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null};null===l?(i=l=c,a=r):l=l.next=c,$f.lanes|=s,Np|=s}u=u.next}while(null!==u&&u!==o);null===l?a=r:l.next=i,ts(r,t.memoizedState)||(Md=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=l,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function rd(e){var t=ed(),n=t.queue;if(null===n)throw Error(Zo(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var i=o=o.next;do{a=e(a,i.action),i=i.next}while(i!==o);ts(a,t.memoizedState)||(Md=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function od(e,t,n){var r=t._getVersion;r=r(t._source);var o=t._workInProgressVersionPrimary;if(null!==o?e=o===r:(e=e.mutableReadLanes,(e=(Bf&e)===e)&&(t._workInProgressVersionPrimary=r,Uf.push(t))),e)return n(t._source);throw Uf.push(t),Error(Zo(350))}function ad(e,t,n,r){var o=_p;if(null===o)throw Error(Zo(349));var a=t._getVersion,i=a(t._source),l=Hf.current,u=l.useState((function(){return od(o,t,n)})),s=u[1],c=u[0];u=qf;var f=e.memoizedState,d=f.refs,p=d.getSnapshot,v=f.source;f=f.subscribe;var h=$f;return e.memoizedState={refs:d,source:t,subscribe:r},l.useEffect((function(){d.getSnapshot=n,d.setSnapshot=s;var e=a(t._source);if(!ts(i,e)){e=n(t._source),ts(c,e)||(s(e),e=av(h),o.mutableReadLanes|=e&o.pendingLanes),e=o.mutableReadLanes,o.entangledLanes|=e;for(var r=o.entanglements,l=e;0<l;){var u=31-Al(l),f=1<<u;r[u]|=e,l&=~f}}}),[n,t,r]),l.useEffect((function(){return r(t._source,(function(){var e=d.getSnapshot,n=d.setSnapshot;try{n(e(t._source));var r=av(h);o.mutableReadLanes|=r&o.pendingLanes}catch(e){n((function(){throw e}))}}))}),[t,r]),ts(p,n)&&ts(v,t)&&ts(f,r)||((e={pending:null,dispatch:null,lastRenderedReducer:td,lastRenderedState:c}).dispatch=s=Ed.bind(null,$f,e),u.queue=e,u.baseQueue=null,c=od(o,t,n),u.memoizedState=u.baseState=c),c}function id(e,t,n){return ad(ed(),e,t,n)}function ld(e){var t=Zf();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:td,lastRenderedState:e}).dispatch=Ed.bind(null,$f,e),[t.memoizedState,e]}function ud(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=$f.updateQueue)?(t={lastEffect:null},$f.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function sd(e){return e={current:e},Zf().memoizedState=e}function cd(){return ed().memoizedState}function fd(e,t,n,r){var o=Zf();$f.flags|=e,o.memoizedState=ud(1|t,n,void 0,void 0===r?null:r)}function dd(e,t,n,r){var o=ed();r=void 0===r?null:r;var a=void 0;if(null!==Yf){var i=Yf.memoizedState;if(a=i.destroy,null!==r&&Xf(r,i.deps))return void ud(t,n,a,r)}$f.flags|=e,o.memoizedState=ud(1|t,n,a,r)}function pd(e,t){return fd(516,4,e,t)}function vd(e,t){return dd(516,4,e,t)}function hd(e,t){return dd(4,2,e,t)}function md(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function gd(e,t,n){return n=null!=n?n.concat([e]):null,dd(4,2,md.bind(null,t,e),n)}function yd(){}function bd(e,t){var n=ed();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Xf(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function wd(e,t){var n=ed();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Xf(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function kd(e,t){var n=Lc();jc(98>n?98:n,(function(){e(!0)})),jc(97<n?97:n,(function(){var n=Vf.transition;Vf.transition=1;try{e(!1),t()}finally{Vf.transition=n}}))}function Ed(e,t,n){var r=ov(),o=av(e),a={lane:o,action:n,eagerReducer:null,eagerState:null,next:null},i=t.pending;if(null===i?a.next=a:(a.next=i.next,i.next=a),t.pending=a,i=e.alternate,e===$f||null!==i&&i===$f)Qf=Kf=!0;else{if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var l=t.lastRenderedState,u=i(l,n);if(a.eagerReducer=i,a.eagerState=u,ts(u,l))return}catch(e){}iv(e,o,r)}}var Sd={readContext:Xc,useCallback:Gf,useContext:Gf,useEffect:Gf,useImperativeHandle:Gf,useLayoutEffect:Gf,useMemo:Gf,useReducer:Gf,useRef:Gf,useState:Gf,useDebugValue:Gf,useDeferredValue:Gf,useTransition:Gf,useMutableSource:Gf,useOpaqueIdentifier:Gf,unstable_isNewReconciler:!1},Dd={readContext:Xc,useCallback:function(e,t){return Zf().memoizedState=[e,void 0===t?null:t],e},useContext:Xc,useEffect:pd,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,fd(4,2,md.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fd(4,2,e,t)},useMemo:function(e,t){var n=Zf();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Zf();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=Ed.bind(null,$f,e),[r.memoizedState,e]},useRef:sd,useState:ld,useDebugValue:yd,useDeferredValue:function(e){var t=ld(e),n=t[0],r=t[1];return pd((function(){var t=Vf.transition;Vf.transition=1;try{r(e)}finally{Vf.transition=t}}),[e]),n},useTransition:function(){var e=ld(!1),t=e[0];return sd(e=kd.bind(null,e[1])),[e,t]},useMutableSource:function(e,t,n){var r=Zf();return r.memoizedState={refs:{getSnapshot:t,setSnapshot:null},source:e,subscribe:n},ad(r,e,t,n)},useOpaqueIdentifier:function(){if(Nf){var e=!1,t=function(e){return{$$typeof:Ma,toString:e,valueOf:e}}((function(){throw e||(e=!0,n("r:"+(Ws++).toString(36))),Error(Zo(355))})),n=ld(t)[1];return!(2&$f.mode)&&($f.flags|=516,ud(5,(function(){n("r:"+(Ws++).toString(36))}),void 0,null)),t}return ld(t="r:"+(Ws++).toString(36)),t},unstable_isNewReconciler:!1},_d={readContext:Xc,useCallback:bd,useContext:Xc,useEffect:vd,useImperativeHandle:gd,useLayoutEffect:hd,useMemo:wd,useReducer:nd,useRef:cd,useState:function(){return nd(td)},useDebugValue:yd,useDeferredValue:function(e){var t=nd(td),n=t[0],r=t[1];return vd((function(){var t=Vf.transition;Vf.transition=1;try{r(e)}finally{Vf.transition=t}}),[e]),n},useTransition:function(){var e=nd(td)[0];return[cd().current,e]},useMutableSource:id,useOpaqueIdentifier:function(){return nd(td)[0]},unstable_isNewReconciler:!1},xd={readContext:Xc,useCallback:bd,useContext:Xc,useEffect:vd,useImperativeHandle:gd,useLayoutEffect:hd,useMemo:wd,useReducer:rd,useRef:cd,useState:function(){return rd(td)},useDebugValue:yd,useDeferredValue:function(e){var t=rd(td),n=t[0],r=t[1];return vd((function(){var t=Vf.transition;Vf.transition=1;try{r(e)}finally{Vf.transition=t}}),[e]),n},useTransition:function(){var e=rd(td)[0];return[cd().current,e]},useMutableSource:id,useOpaqueIdentifier:function(){return rd(td)[0]},unstable_isNewReconciler:!1},Od=va.ReactCurrentOwner,Md=!1;function Cd(e,t,n,r){t.child=null===e?bf(t,null,n,r):yf(t,e.child,n,r)}function Td(e,t,n,r,o){n=n.render;var a=t.ref;return Gc(t,o),r=Jf(e,t,n,r,a,o),null===e||Md?(t.flags|=1,Cd(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~o,Xd(e,t,o))}function Pd(e,t,n,r,o,a){if(null===e){var i=n.type;return"function"!=typeof i||jv(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Iv(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Rd(e,t,i,r,o,a))}return i=e.child,o&a||(o=i.memoizedProps,!(n=null!==(n=n.compare)?n:rs)(o,r)||e.ref!==t.ref)?(t.flags|=1,(e=Fv(i,r)).ref=t.ref,e.return=t,t.child=e):Xd(e,t,a)}function Rd(e,t,n,r,o,a){if(null!==e&&rs(e.memoizedProps,r)&&e.ref===t.ref){if(Md=!1,!(a&o))return t.lanes=e.lanes,Xd(e,t,a);16384&e.flags&&(Md=!0)}return Ld(e,t,n,r,a)}function Nd(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode||"unstable-defer-without-hiding"===r.mode)if(4&t.mode){if(!(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e},vv(t,e),null;t.memoizedState={baseLanes:0},vv(t,null!==a?a.baseLanes:n)}else t.memoizedState={baseLanes:0},vv(t,n);else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,vv(t,r);return Cd(e,t,o,n),t.child}function zd(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=128)}function Ld(e,t,n,r,o){var a=uc(n)?ic:oc.current;return a=lc(t,a),Gc(t,o),n=Jf(e,t,n,r,a,o),null===e||Md?(t.flags|=1,Cd(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~o,Xd(e,t,o))}function Ad(e,t,n,r,o){if(uc(n)){var a=!0;dc(t)}else a=!1;if(Gc(t,o),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),ff(t,n,r),pf(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var u=i.context,s=n.contextType;"object"==typeof s&&null!==s?s=Xc(s):s=lc(t,s=uc(n)?ic:oc.current);var c=n.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof i.getSnapshotBeforeUpdate;f||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==r||u!==s)&&df(t,i,r,s),Jc=!1;var d=t.memoizedState;i.state=d,of(t,r,i,o),u=t.memoizedState,l!==r||d!==u||ac.current||Jc?("function"==typeof c&&(uf(t,n,c,r),u=t.memoizedState),(l=Jc||cf(t,n,l,r,d,u,s))?(f||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4)):("function"==typeof i.componentDidMount&&(t.flags|=4),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=s,r=l):("function"==typeof i.componentDidMount&&(t.flags|=4),r=!1)}else{i=t.stateNode,ef(e,t),l=t.memoizedProps,s=t.type===t.elementType?l:Hc(t.type,l),i.props=s,f=t.pendingProps,d=i.context,"object"==typeof(u=n.contextType)&&null!==u?u=Xc(u):u=lc(t,u=uc(n)?ic:oc.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==f||d!==u)&&df(t,i,r,u),Jc=!1,d=t.memoizedState,i.state=d,of(t,r,i,o);var v=t.memoizedState;l!==f||d!==v||ac.current||Jc?("function"==typeof p&&(uf(t,n,p,r),v=t.memoizedState),(s=Jc||cf(t,n,s,r,d,v,u))?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,v,u),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,v,u)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=256)):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=256),t.memoizedProps=r,t.memoizedState=v),i.props=r,i.state=v,i.context=u,r=s):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=256),r=!1)}return jd(e,t,n,r,a,o)}function jd(e,t,n,r,o,a){zd(e,t);var i=!!(64&t.flags);if(!r&&!i)return o&&pc(t,n,!1),Xd(e,t,a);r=t.stateNode,Od.current=t;var l=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=yf(t,e.child,null,a),t.child=yf(t,null,l,a)):Cd(e,t,l,a),t.memoizedState=r.state,o&&pc(t,n,!0),t.child}function Fd(e){var t=e.stateNode;t.pendingContext?cc(0,t.pendingContext,t.pendingContext!==t.context):t.context&&cc(0,t.context,!1),_f(e,t.containerInfo)}var Id,Ud,Wd,Hd,Vd={dehydrated:null,retryLane:0};function Bd(e,t,n){var r,o=t.pendingProps,a=Cf.current,i=!1;return(r=!!(64&t.flags))||(r=(null===e||null!==e.memoizedState)&&!!(2&a)),r?(i=!0,t.flags&=-65):null!==e&&null===e.memoizedState||void 0===o.fallback||!0===o.unstable_avoidThisFallback||(a|=1),nc(Cf,1&a),null===e?(void 0!==o.fallback&&Af(t),e=o.children,a=o.fallback,i?(e=$d(t,e,a,n),t.child.memoizedState={baseLanes:n},t.memoizedState=Vd,e):"number"==typeof o.unstable_expectedLoadTime?(e=$d(t,e,a,n),t.child.memoizedState={baseLanes:n},t.memoizedState=Vd,t.lanes=33554432,e):((n=Wv({mode:"visible",children:e},t.mode,n,null)).return=t,t.child=n)):(e.memoizedState,i?(o=qd(e,t,o.children,o.fallback,n),i=t.child,a=e.child.memoizedState,i.memoizedState=null===a?{baseLanes:n}:{baseLanes:a.baseLanes|n},i.childLanes=e.childLanes&~n,t.memoizedState=Vd,o):(n=Yd(e,t,o.children,n),t.memoizedState=null,n))}function $d(e,t,n,r){var o=e.mode,a=e.child;return t={mode:"hidden",children:t},2&o||null===a?a=Wv(t,o,0,null):(a.childLanes=0,a.pendingProps=t),n=Uv(n,o,r,null),a.return=e,n.return=e,a.sibling=n,e.child=a,n}function Yd(e,t,n,r){var o=e.child;return e=o.sibling,n=Fv(o,{mode:"visible",children:n}),!(2&t.mode)&&(n.lanes=r),n.return=t,n.sibling=null,null!==e&&(e.nextEffect=null,e.flags=8,t.firstEffect=t.lastEffect=e),t.child=n}function qd(e,t,n,r,o){var a=t.mode,i=e.child;e=i.sibling;var l={mode:"hidden",children:n};return 2&a||t.child===i?n=Fv(i,l):((n=t.child).childLanes=0,n.pendingProps=l,null!==(i=n.lastEffect)?(t.firstEffect=n.firstEffect,t.lastEffect=i,i.nextEffect=null):t.firstEffect=t.lastEffect=null),null!==e?r=Fv(e,r):(r=Uv(r,a,o,null)).flags|=2,r.return=t,n.return=t,n.sibling=r,t.child=n,r}function Kd(e,t){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),Qc(e.return,t)}function Qd(e,t,n,r,o,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o,lastEffect:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o,i.lastEffect=a)}function Gd(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(Cd(e,t,r.children,n),2&(r=Cf.current))r=1&r|2,t.flags|=64;else{if(null!==e&&64&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Kd(e,n);else if(19===e.tag)Kd(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(nc(Cf,r),2&t.mode)switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===Tf(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Qd(t,!1,o,n,a,t.lastEffect);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===Tf(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Qd(t,!0,n,null,a,t.lastEffect);break;case"together":Qd(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Xd(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Np|=t.lanes,n&t.childLanes){if(null!==e&&t.child!==e.child)throw Error(Zo(153));if(null!==t.child){for(n=Fv(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Fv(e,e.pendingProps)).return=t;n.sibling=null}return t.child}return null}function Jd(e,t){if(!Nf)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Zd(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:case 17:return uc(t.type)&&sc(),null;case 3:return xf(),tc(ac),tc(oc),Wf(),(r=t.stateNode).pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(Ff(t)?t.flags|=4:r.hydrate||(t.flags|=256)),Ud(t),null;case 5:Mf(t);var o=Df(Sf.current);if(n=t.type,null!==e&&null!=t.stateNode)Wd(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=128);else{if(!r){if(null===t.stateNode)throw Error(Zo(166));return null}if(e=Df(kf.current),Ff(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[Vs]=t,r[Bs]=a,n){case"dialog":ks("cancel",r),ks("close",r);break;case"iframe":case"object":case"embed":ks("load",r);break;case"video":case"audio":for(e=0;e<gs.length;e++)ks(gs[e],r);break;case"source":ks("error",r);break;case"img":case"image":case"link":ks("error",r),ks("load",r);break;case"details":ks("toggle",r);break;case"input":qa(r,a),ks("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},ks("invalid",r);break;case"textarea":ti(r,a),ks("invalid",r)}for(var i in mi(n,a),e=null,a)a.hasOwnProperty(i)&&(o=a[i],"children"===i?"string"==typeof o?r.textContent!==o&&(e=["children",o]):"number"==typeof o&&r.textContent!==""+o&&(e=["children",""+o]):ta.hasOwnProperty(i)&&null!=o&&"onScroll"===i&&ks("scroll",r));switch(n){case"input":Va(r),Ga(r,a,!0);break;case"textarea":Va(r),ri(r);break;case"select":case"option":break;default:"function"==typeof a.onClick&&(r.onclick=Ps)}r=e,t.updateQueue=r,null!==r&&(t.flags|=4)}else{switch(i=9===o.nodeType?o:o.ownerDocument,e===oi.html&&(e=ai(n)),e===oi.html?"script"===n?((e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),"select"===n&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Vs]=t,e[Bs]=r,Id(e,t,!1,!1),t.stateNode=e,i=gi(n,r),n){case"dialog":ks("cancel",e),ks("close",e),o=r;break;case"iframe":case"object":case"embed":ks("load",e),o=r;break;case"video":case"audio":for(o=0;o<gs.length;o++)ks(gs[o],e);o=r;break;case"source":ks("error",e),o=r;break;case"img":case"image":case"link":ks("error",e),ks("load",e),o=r;break;case"details":ks("toggle",e),o=r;break;case"input":qa(e,r),o=Ya(e,r),ks("invalid",e);break;case"option":o=Ja(e,r);break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Xo({},r,{value:void 0}),ks("invalid",e);break;case"textarea":ti(e,r),o=ei(e,r),ks("invalid",e);break;default:o=r}mi(n,o);var l=o;for(a in l)if(l.hasOwnProperty(a)){var u=l[a];"style"===a?vi(e,u):"dangerouslySetInnerHTML"===a?null!=(u=u?u.__html:void 0)&&si(e,u):"children"===a?"string"==typeof u?("textarea"!==n||""!==u)&&ci(e,u):"number"==typeof u&&ci(e,""+u):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(ta.hasOwnProperty(a)?null!=u&&"onScroll"===a&&ks("scroll",e):null!=u&&pa(e,a,u,i))}switch(n){case"input":Va(e),Ga(e,r,!1);break;case"textarea":Va(e),ri(e);break;case"option":null!=r.value&&e.setAttribute("value",""+Wa(r.value));break;case"select":e.multiple=!!r.multiple,null!=(a=r.value)?Za(e,!!r.multiple,a,!1):null!=r.defaultValue&&Za(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof o.onClick&&(e.onclick=Ps)}zs(n,r)&&(t.flags|=4)}null!==t.ref&&(t.flags|=128)}return null;case 6:if(e&&null!=t.stateNode)Hd(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(Zo(166));n=Df(Sf.current),Df(kf.current),Ff(t)?(r=t.stateNode,n=t.memoizedProps,r[Vs]=t,r.nodeValue!==n&&(t.flags|=4)):((r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Vs]=t,t.stateNode=r)}return null;case 13:return tc(Cf),r=t.memoizedState,64&t.flags?(t.lanes=n,t):(r=null!==r,n=!1,null===e?void 0!==t.memoizedProps.fallback&&Ff(t):n=null!==e.memoizedState,r&&!n&&2&t.mode&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||1&Cf.current?0===Tp&&(Tp=3):(0!==Tp&&3!==Tp||(Tp=4),null===_p||!(134217727&Np)&&!(134217727&zp)||cv(_p,Op))),(r||n)&&(t.flags|=4),null);case 4:return xf(),Ud(t),null===e&&Ss(t.stateNode.containerInfo),null;case 10:return Kc(t),null;case 19:if(tc(Cf),null===(r=t.memoizedState))return null;if(a=!!(64&t.flags),null===(i=r.rendering))if(a)Jd(r,!1);else{if(0!==Tp||null!==e&&64&e.flags)for(e=t.child;null!==e;){if(null!==(i=Tf(e))){for(t.flags|=64,Jd(r,!1),null!==(a=i.updateQueue)&&(t.updateQueue=a,t.flags|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,r=n,n=t.child;null!==n;)e=r,(a=n).flags&=2,a.nextEffect=null,a.firstEffect=null,a.lastEffect=null,null===(i=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=i.childLanes,a.lanes=i.lanes,a.child=i.child,a.memoizedProps=i.memoizedProps,a.memoizedState=i.memoizedState,a.updateQueue=i.updateQueue,a.type=i.type,e=i.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return nc(Cf,1&Cf.current|2),t.child}e=e.sibling}null!==r.tail&&zc()>Fp&&(t.flags|=64,a=!0,Jd(r,!1),t.lanes=33554432)}else{if(!a)if(null!==(e=Tf(i))){if(t.flags|=64,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Jd(r,!0),null===r.tail&&"hidden"===r.tailMode&&!i.alternate&&!Nf)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*zc()-r.renderingStartTime>Fp&&1073741824!==n&&(t.flags|=64,a=!0,Jd(r,!1),t.lanes=33554432);r.isBackwards?(i.sibling=t.child,t.child=i):(null!==(n=r.last)?n.sibling=i:t.child=i,r.last=i)}return null!==r.tail?(n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=zc(),n.sibling=null,t=Cf.current,nc(Cf,a?1&t|2:1&t),n):null;case 23:case 24:return hv(),null!==e&&null!==e.memoizedState!=(null!==t.memoizedState)&&"unstable-defer-without-hiding"!==r.mode&&(t.flags|=4),null}throw Error(Zo(156,t.tag))}function ep(e){switch(e.tag){case 1:uc(e.type)&&sc();var t=e.flags;return 4096&t?(e.flags=-4097&t|64,e):null;case 3:if(xf(),tc(ac),tc(oc),Wf(),64&(t=e.flags))throw Error(Zo(285));return e.flags=-4097&t|64,e;case 5:return Mf(e),null;case 13:return tc(Cf),4096&(t=e.flags)?(e.flags=-4097&t|64,e):null;case 19:return tc(Cf),null;case 4:return xf(),null;case 10:return Kc(e),null;case 23:case 24:return hv(),null;default:return null}}function tp(e,t){try{var n="",r=t;do{n+=Ia(r),r=r.return}while(r);var o=n}catch(e){o="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:o}}function np(e,t){try{console.error(t.value)}catch(e){setTimeout((function(){throw e}))}}Id=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ud=function(){},Wd=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Df(kf.current);var a,i=null;switch(n){case"input":o=Ya(e,o),r=Ya(e,r),i=[];break;case"option":o=Ja(e,o),r=Ja(e,r),i=[];break;case"select":o=Xo({},o,{value:void 0}),r=Xo({},r,{value:void 0}),i=[];break;case"textarea":o=ei(e,o),r=ei(e,r),i=[];break;default:"function"!=typeof o.onClick&&"function"==typeof r.onClick&&(e.onclick=Ps)}for(s in mi(n,r),n=null,o)if(!r.hasOwnProperty(s)&&o.hasOwnProperty(s)&&null!=o[s])if("style"===s){var l=o[s];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==s&&"children"!==s&&"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(ta.hasOwnProperty(s)?i||(i=[]):(i=i||[]).push(s,null));for(s in r){var u=r[s];if(l=null!=o?o[s]:void 0,r.hasOwnProperty(s)&&u!==l&&(null!=u||null!=l))if("style"===s)if(l){for(a in l)!l.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&l[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(i||(i=[]),i.push(s,n)),n=u;else"dangerouslySetInnerHTML"===s?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(i=i||[]).push(s,u)):"children"===s?"string"!=typeof u&&"number"!=typeof u||(i=i||[]).push(s,""+u):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&(ta.hasOwnProperty(s)?(null!=u&&"onScroll"===s&&ks("scroll",e),i||l===u||(i=[])):"object"==typeof u&&null!==u&&u.$$typeof===Ma?u.toString():(i=i||[]).push(s,u))}n&&(i=i||[]).push("style",n);var s=i;(t.updateQueue=s)&&(t.flags|=4)}},Hd=function(e,t,n,r){n!==r&&(t.flags|=4)};var rp="function"==typeof WeakMap?WeakMap:Map;function op(e,t,n){(n=tf(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hp||(Hp=!0,Vp=r),np(0,t)},n}function ap(e,t,n){(n=tf(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var o=t.value;n.payload=function(){return np(0,t),r(o)}}var a=e.stateNode;return null!==a&&"function"==typeof a.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===Bp?Bp=new Set([this]):Bp.add(this),np(0,t));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}var ip="function"==typeof WeakSet?WeakSet:Set;function lp(e){var t=e.ref;if(null!==t)if("function"==typeof t)try{t(null)}catch(t){Rv(e,t)}else t.current=null}function up(e,t){switch(t.tag){case 0:case 11:case 15:case 22:case 5:case 6:case 4:case 17:return;case 1:if(256&t.flags&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:Hc(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:return void(256&t.flags&&Fs(t.stateNode.containerInfo))}throw Error(Zo(163))}function sp(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{if(!(3&~e.tag)){var r=e.create;e.destroy=r()}e=e.next}while(e!==t)}if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{var o=e;r=o.next,4&(o=o.tag)&&1&o&&(Cv(n,e),Mv(n,e)),e=r}while(e!==t)}return;case 1:return e=n.stateNode,4&n.flags&&(null===t?e.componentDidMount():(r=n.elementType===n.type?t.memoizedProps:Hc(n.type,t.memoizedProps),e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate))),void(null!==(t=n.updateQueue)&&af(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:case 1:e=n.child.stateNode}af(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.flags&&zs(n.type,n.memoizedProps)&&e.focus());case 6:case 4:case 12:case 19:case 17:case 20:case 21:case 23:case 24:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&vl(n)))))}throw Error(Zo(163))}function cp(e,t){for(var n=e;;){if(5===n.tag){var r=n.stateNode;if(t)"function"==typeof(r=r.style).setProperty?r.setProperty("display","none","important"):r.display="none";else{r=n.stateNode;var o=n.memoizedProps.style;o=null!=o&&o.hasOwnProperty("display")?o.display:null,r.style.display=pi("display",o)}}else if(6===n.tag)n.stateNode.nodeValue=t?"":n.memoizedProps;else if((23!==n.tag&&24!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}}function fp(e,t){if(hc&&"function"==typeof hc.onCommitFiberUnmount)try{hc.onCommitFiberUnmount(vc,t)}catch(e){}switch(t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var n=e=e.next;do{var r=n,o=r.destroy;if(r=r.tag,void 0!==o)if(4&r)Cv(t,n);else{r=t;try{o()}catch(e){Rv(r,e)}}n=n.next}while(n!==e)}break;case 1:if(lp(t),"function"==typeof(e=t.stateNode).componentWillUnmount)try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(e){Rv(t,e)}break;case 5:lp(t);break;case 4:gp(e,t)}}function dp(e){e.alternate=null,e.child=null,e.dependencies=null,e.firstEffect=null,e.lastEffect=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.return=null,e.updateQueue=null}function pp(e){return 5===e.tag||3===e.tag||4===e.tag}function vp(e){e:{for(var t=e.return;null!==t;){if(pp(t))break e;t=t.return}throw Error(Zo(160))}var n=t;switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(Zo(161))}16&n.flags&&(ci(t,""),n.flags&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||pp(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.flags)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.flags)){n=n.stateNode;break e}}r?hp(e,n,t):mp(e,n,t)}function hp(e,t,n){var r=e.tag,o=5===r||6===r;if(o)e=o?e.stateNode:e.stateNode.instance,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Ps));else if(4!==r&&null!==(e=e.child))for(hp(e,t,n),e=e.sibling;null!==e;)hp(e,t,n),e=e.sibling}function mp(e,t,n){var r=e.tag,o=5===r||6===r;if(o)e=o?e.stateNode:e.stateNode.instance,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(mp(e,t,n),e=e.sibling;null!==e;)mp(e,t,n),e=e.sibling}function gp(e,t){for(var n,r,o=t,a=!1;;){if(!a){a=o.return;e:for(;;){if(null===a)throw Error(Zo(160));switch(n=a.stateNode,a.tag){case 5:r=!1;break e;case 3:case 4:n=n.containerInfo,r=!0;break e}a=a.return}a=!0}if(5===o.tag||6===o.tag){e:for(var i=e,l=o,u=l;;)if(fp(i,u),null!==u.child&&4!==u.tag)u.child.return=u,u=u.child;else{if(u===l)break e;for(;null===u.sibling;){if(null===u.return||u.return===l)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}r?(i=n,l=o.stateNode,8===i.nodeType?i.parentNode.removeChild(l):i.removeChild(l)):n.removeChild(o.stateNode)}else if(4===o.tag){if(null!==o.child){n=o.stateNode.containerInfo,r=!0,o.child.return=o,o=o.child;continue}}else if(fp(e,o),null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break;for(;null===o.sibling;){if(null===o.return||o.return===t)return;4===(o=o.return).tag&&(a=!1)}o.sibling.return=o.return,o=o.sibling}}function yp(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:var n=t.updateQueue;if(null!==(n=null!==n?n.lastEffect:null)){var r=n=n.next;do{!(3&~r.tag)&&(e=r.destroy,r.destroy=void 0,void 0!==e&&e()),r=r.next}while(r!==n)}return;case 1:case 12:case 17:return;case 5:if(null!=(n=t.stateNode)){r=t.memoizedProps;var o=null!==e?e.memoizedProps:r;e=t.type;var a=t.updateQueue;if(t.updateQueue=null,null!==a){for(n[Bs]=r,"input"===e&&"radio"===r.type&&null!=r.name&&Ka(n,r),gi(e,o),t=gi(e,r),o=0;o<a.length;o+=2){var i=a[o],l=a[o+1];"style"===i?vi(n,l):"dangerouslySetInnerHTML"===i?si(n,l):"children"===i?ci(n,l):pa(n,i,l,t)}switch(e){case"input":Qa(n,r);break;case"textarea":ni(n,r);break;case"select":e=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(a=r.value)?Za(n,!!r.multiple,a,!1):e!==!!r.multiple&&(null!=r.defaultValue?Za(n,!!r.multiple,r.defaultValue,!0):Za(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(Zo(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((n=t.stateNode).hydrate&&(n.hydrate=!1,vl(n.containerInfo)));case 13:return null!==t.memoizedState&&(jp=zc(),cp(t.child,!0)),void bp(t);case 19:return void bp(t);case 23:case 24:return void cp(t,null!==t.memoizedState)}throw Error(Zo(163))}function bp(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new ip),t.forEach((function(t){var r=zv.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function wp(e,t){return null!==e&&(null===(e=e.memoizedState)||null!==e.dehydrated)&&(null!==(t=t.memoizedState)&&null===t.dehydrated)}var kp=Math.ceil,Ep=va.ReactCurrentDispatcher,Sp=va.ReactCurrentOwner,Dp=0,_p=null,xp=null,Op=0,Mp=0,Cp=ec(0),Tp=0,Pp=null,Rp=0,Np=0,zp=0,Lp=0,Ap=null,jp=0,Fp=1/0;function Ip(){Fp=zc()+500}var Up,Wp=null,Hp=!1,Vp=null,Bp=null,$p=!1,Yp=null,qp=90,Kp=[],Qp=[],Gp=null,Xp=0,Jp=null,Zp=-1,ev=0,tv=0,nv=null,rv=!1;function ov(){return 48&Dp?zc():-1!==Zp?Zp:Zp=zc()}function av(e){if(!(2&(e=e.mode)))return 1;if(!(4&e))return 99===Lc()?1:2;if(0===ev&&(ev=Rp),0!==Wc.transition){0!==tv&&(tv=null!==Ap?Ap.pendingLanes:0),e=ev;var t=4186112&~tv;return 0===(t&=-t)&&(0===(t=(e=4186112&~e)&-e)&&(t=8192)),t}return e=Lc(),4&Dp&&98===e?e=Rl(12,ev):e=Rl(e=function(e){switch(e){case 99:return 15;case 98:return 10;case 97:case 96:return 8;case 95:return 2;default:return 0}}(e),ev),e}function iv(e,t,n){if(50<Xp)throw Xp=0,Jp=null,Error(Zo(185));if(null===(e=lv(e,t)))return null;Ll(e,t,n),e===_p&&(zp|=t,4===Tp&&cv(e,Op));var r=Lc();1===t?8&Dp&&!(48&Dp)?fv(e):(uv(e,n),0===Dp&&(Ip(),Ic())):(!(4&Dp)||98!==r&&99!==r||(null===Gp?Gp=new Set([e]):Gp.add(e)),uv(e,n)),Ap=e}function lv(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}function uv(e,t){for(var n=e.callbackNode,r=e.suspendedLanes,o=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-Al(i),u=1<<l,s=a[l];if(-1===s){if(!(u&r)||u&o){s=t,Cl(u);var c=Ml;a[l]=10<=c?s+250:6<=c?s+5e3:-1}}else s<=t&&(e.expiredLanes|=u);i&=~u}if(r=Tl(e,e===_p?Op:0),t=Ml,0===r)null!==n&&(n!==Mc&&yc(n),e.callbackNode=null,e.callbackPriority=0);else{if(null!==n){if(e.callbackPriority===t)return;n!==Mc&&yc(n)}15===t?(n=fv.bind(null,e),null===Tc?(Tc=[n],Pc=gc(Sc,Uc)):Tc.push(n),n=Mc):14===t?n=Fc(99,fv.bind(null,e)):(n=function(e){switch(e){case 15:case 14:return 99;case 13:case 12:case 11:case 10:return 98;case 9:case 8:case 7:case 6:case 4:case 5:return 97;case 3:case 2:case 1:return 95;case 0:return 90;default:throw Error(Zo(358,e))}}(t),n=Fc(n,sv.bind(null,e))),e.callbackPriority=t,e.callbackNode=n}}function sv(e){if(Zp=-1,tv=ev=0,48&Dp)throw Error(Zo(327));var t=e.callbackNode;if(Ov()&&e.callbackNode!==t)return null;var n=Tl(e,e===_p?Op:0);if(0===n)return null;var r=n,o=Dp;Dp|=16;var a=yv();for(_p===e&&Op===r||(Ip(),mv(e,r));;)try{kv();break}catch(t){gv(e,t)}if(qc(),Ep.current=a,Dp=o,null!==xp?r=0:(_p=null,Op=0,r=Tp),Rp&zp)mv(e,0);else if(0!==r){if(2===r&&(Dp|=64,e.hydrate&&(e.hydrate=!1,Fs(e.containerInfo)),0!==(n=Pl(e))&&(r=bv(e,n))),1===r)throw t=Pp,mv(e,0),cv(e,n),uv(e,zc()),t;switch(e.finishedWork=e.current.alternate,e.finishedLanes=n,r){case 0:case 1:throw Error(Zo(345));case 2:case 5:Dv(e);break;case 3:if(cv(e,n),(62914560&n)===n&&10<(r=jp+500-zc())){if(0!==Tl(e,0))break;if(((o=e.suspendedLanes)&n)!==n){ov(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=As(Dv.bind(null,e),r);break}Dv(e);break;case 4:if(cv(e,n),(4186112&n)===n)break;for(r=e.eventTimes,o=-1;0<n;){var i=31-Al(n);a=1<<i,(i=r[i])>o&&(o=i),n&=~a}if(n=o,10<(n=(120>(n=zc()-n)?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*kp(n/1960))-n)){e.timeoutHandle=As(Dv.bind(null,e),n);break}Dv(e);break;default:throw Error(Zo(329))}}return uv(e,zc()),e.callbackNode===t?sv.bind(null,e):null}function cv(e,t){for(t&=~Lp,t&=~zp,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Al(t),r=1<<n;e[n]=-1,t&=~r}}function fv(e){if(48&Dp)throw Error(Zo(327));if(Ov(),e===_p&&e.expiredLanes&Op){var t=Op,n=bv(e,t);Rp&zp&&(n=bv(e,t=Tl(e,t)))}else n=bv(e,t=Tl(e,0));if(0!==e.tag&&2===n&&(Dp|=64,e.hydrate&&(e.hydrate=!1,Fs(e.containerInfo)),0!==(t=Pl(e))&&(n=bv(e,t))),1===n)throw n=Pp,mv(e,0),cv(e,t),uv(e,zc()),n;return e.finishedWork=e.current.alternate,e.finishedLanes=t,Dv(e),uv(e,zc()),null}function dv(e,t){var n=Dp;Dp|=1;try{return e(t)}finally{0===(Dp=n)&&(Ip(),Ic())}}function pv(e,t){var n=Dp;Dp&=-2,Dp|=8;try{return e(t)}finally{0===(Dp=n)&&(Ip(),Ic())}}function vv(e,t){nc(Cp,Mp),Mp|=t,Rp|=t}function hv(){Mp=Cp.current,tc(Cp)}function mv(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,js(n)),null!==xp)for(n=xp.return;null!==n;){var r=n;switch(r.tag){case 1:null!=(r=r.type.childContextTypes)&&sc();break;case 3:xf(),tc(ac),tc(oc),Wf();break;case 5:Mf(r);break;case 4:xf();break;case 13:case 19:tc(Cf);break;case 10:Kc(r);break;case 23:case 24:hv()}n=n.return}_p=e,xp=Fv(e.current,null),Op=Mp=Rp=t,Tp=0,Pp=null,Lp=zp=Np=0}function gv(e,t){for(;;){var n=xp;try{if(qc(),Hf.current=Sd,Kf){for(var r=$f.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}Kf=!1}if(Bf=0,qf=Yf=$f=null,Qf=!1,Sp.current=null,null===n||null===n.return){Tp=1,Pp=t,xp=null;break}e:{var a=e,i=n.return,l=n,u=t;if(t=Op,l.flags|=2048,l.firstEffect=l.lastEffect=null,null!==u&&"object"==typeof u&&"function"==typeof u.then){var s=u;if(!(2&l.mode)){var c=l.alternate;c?(l.updateQueue=c.updateQueue,l.memoizedState=c.memoizedState,l.lanes=c.lanes):(l.updateQueue=null,l.memoizedState=null)}var f=!!(1&Cf.current),d=i;do{var p;if(p=13===d.tag){var v=d.memoizedState;if(null!==v)p=null!==v.dehydrated;else{var h=d.memoizedProps;p=void 0!==h.fallback&&(!0!==h.unstable_avoidThisFallback||!f)}}if(p){var m=d.updateQueue;if(null===m){var g=new Set;g.add(s),d.updateQueue=g}else m.add(s);if(!(2&d.mode)){if(d.flags|=64,l.flags|=16384,l.flags&=-2981,1===l.tag)if(null===l.alternate)l.tag=17;else{var y=tf(-1,1);y.tag=2,nf(l,y)}l.lanes|=1;break e}u=void 0,l=t;var b=a.pingCache;if(null===b?(b=a.pingCache=new rp,u=new Set,b.set(s,u)):void 0===(u=b.get(s))&&(u=new Set,b.set(s,u)),!u.has(l)){u.add(l);var w=Nv.bind(null,a,s,l);s.then(w,w)}d.flags|=4096,d.lanes=t;break e}d=d.return}while(null!==d);u=Error((Ua(l.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display.")}5!==Tp&&(Tp=2),u=tp(u,l),d=i;do{switch(d.tag){case 3:a=u,d.flags|=4096,t&=-t,d.lanes|=t,rf(d,op(0,a,t));break e;case 1:a=u;var k=d.type,E=d.stateNode;if(!(64&d.flags||"function"!=typeof k.getDerivedStateFromError&&(null===E||"function"!=typeof E.componentDidCatch||null!==Bp&&Bp.has(E)))){d.flags|=4096,t&=-t,d.lanes|=t,rf(d,ap(d,a,t));break e}}d=d.return}while(null!==d)}Sv(n)}catch(e){t=e,xp===n&&null!==n&&(xp=n=n.return);continue}break}}function yv(){var e=Ep.current;return Ep.current=Sd,null===e?Sd:e}function bv(e,t){var n=Dp;Dp|=16;var r=yv();for(_p===e&&Op===t||mv(e,t);;)try{wv();break}catch(t){gv(e,t)}if(qc(),Dp=n,Ep.current=r,null!==xp)throw Error(Zo(261));return _p=null,Op=0,Tp}function wv(){for(;null!==xp;)Ev(xp)}function kv(){for(;null!==xp&&!bc();)Ev(xp)}function Ev(e){var t=Up(e.alternate,e,Mp);e.memoizedProps=e.pendingProps,null===t?Sv(e):xp=t,Sp.current=null}function Sv(e){var t=e;do{var n=t.alternate;if(e=t.return,2048&t.flags){if(null!==(n=ep(t)))return n.flags&=2047,void(xp=n);null!==e&&(e.firstEffect=e.lastEffect=null,e.flags|=2048)}else{if(null!==(n=Zd(n,t,Mp)))return void(xp=n);if(24!==(n=t).tag&&23!==n.tag||null===n.memoizedState||1073741824&Mp||!(4&n.mode)){for(var r=0,o=n.child;null!==o;)r|=o.lanes|o.childLanes,o=o.sibling;n.childLanes=r}null!==e&&!(2048&e.flags)&&(null===e.firstEffect&&(e.firstEffect=t.firstEffect),null!==t.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=t.firstEffect),e.lastEffect=t.lastEffect),1<t.flags&&(null!==e.lastEffect?e.lastEffect.nextEffect=t:e.firstEffect=t,e.lastEffect=t))}if(null!==(t=t.sibling))return void(xp=t);xp=t=e}while(null!==t);0===Tp&&(Tp=5)}function Dv(e){var t=Lc();return jc(99,_v.bind(null,e,t)),null}function _v(e,t){do{Ov()}while(null!==Yp);if(48&Dp)throw Error(Zo(327));var n=e.finishedWork;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(Zo(177));e.callbackNode=null;var r=n.lanes|n.childLanes,o=r,a=e.pendingLanes&~o;e.pendingLanes=o,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=o,e.mutableReadLanes&=o,e.entangledLanes&=o,o=e.entanglements;for(var i=e.eventTimes,l=e.expirationTimes;0<a;){var u=31-Al(a),s=1<<u;o[u]=0,i[u]=-1,l[u]=-1,a&=~s}if(null!==Gp&&!(24&r)&&Gp.has(e)&&Gp.delete(e),e===_p&&(xp=_p=null,Op=0),1<n.flags?null!==n.lastEffect?(n.lastEffect.nextEffect=n,r=n.firstEffect):r=n:r=n.firstEffect,null!==r){if(o=Dp,Dp|=32,Sp.current=null,Rs=Wl,us(i=ls())){if("selectionStart"in i)l={start:i.selectionStart,end:i.selectionEnd};else e:if(l=(l=i.ownerDocument)&&l.defaultView||window,(s=l.getSelection&&l.getSelection())&&0!==s.rangeCount){l=s.anchorNode,a=s.anchorOffset,u=s.focusNode,s=s.focusOffset;try{l.nodeType,u.nodeType}catch(e){l=null;break e}var c=0,f=-1,d=-1,p=0,v=0,h=i,m=null;t:for(;;){for(var g;h!==l||0!==a&&3!==h.nodeType||(f=c+a),h!==u||0!==s&&3!==h.nodeType||(d=c+s),3===h.nodeType&&(c+=h.nodeValue.length),null!==(g=h.firstChild);)m=h,h=g;for(;;){if(h===i)break t;if(m===l&&++p===a&&(f=c),m===u&&++v===s&&(d=c),null!==(g=h.nextSibling))break;m=(h=m).parentNode}h=g}l=-1===f||-1===d?null:{start:f,end:d}}else l=null;l=l||{start:0,end:0}}else l=null;Ns={focusedElem:i,selectionRange:l},Wl=!1,nv=null,rv=!1,Wp=r;do{try{xv()}catch(e){if(null===Wp)throw Error(Zo(330));Rv(Wp,e),Wp=Wp.nextEffect}}while(null!==Wp);nv=null,Wp=r;do{try{for(i=e;null!==Wp;){var y=Wp.flags;if(16&y&&ci(Wp.stateNode,""),128&y){var b=Wp.alternate;if(null!==b){var w=b.ref;null!==w&&("function"==typeof w?w(null):w.current=null)}}switch(1038&y){case 2:vp(Wp),Wp.flags&=-3;break;case 6:vp(Wp),Wp.flags&=-3,yp(Wp.alternate,Wp);break;case 1024:Wp.flags&=-1025;break;case 1028:Wp.flags&=-1025,yp(Wp.alternate,Wp);break;case 4:yp(Wp.alternate,Wp);break;case 8:gp(i,l=Wp);var k=l.alternate;dp(l),null!==k&&dp(k)}Wp=Wp.nextEffect}}catch(e){if(null===Wp)throw Error(Zo(330));Rv(Wp,e),Wp=Wp.nextEffect}}while(null!==Wp);if(w=Ns,b=ls(),y=w.focusedElem,i=w.selectionRange,b!==y&&y&&y.ownerDocument&&is(y.ownerDocument.documentElement,y)){null!==i&&us(y)&&(b=i.start,void 0===(w=i.end)&&(w=b),"selectionStart"in y?(y.selectionStart=b,y.selectionEnd=Math.min(w,y.value.length)):(w=(b=y.ownerDocument||document)&&b.defaultView||window).getSelection&&(w=w.getSelection(),l=y.textContent.length,k=Math.min(i.start,l),i=void 0===i.end?k:Math.min(i.end,l),!w.extend&&k>i&&(l=i,i=k,k=l),l=as(y,k),a=as(y,i),l&&a&&(1!==w.rangeCount||w.anchorNode!==l.node||w.anchorOffset!==l.offset||w.focusNode!==a.node||w.focusOffset!==a.offset)&&((b=b.createRange()).setStart(l.node,l.offset),w.removeAllRanges(),k>i?(w.addRange(b),w.extend(a.node,a.offset)):(b.setEnd(a.node,a.offset),w.addRange(b))))),b=[];for(w=y;w=w.parentNode;)1===w.nodeType&&b.push({element:w,left:w.scrollLeft,top:w.scrollTop});for("function"==typeof y.focus&&y.focus(),y=0;y<b.length;y++)(w=b[y]).element.scrollLeft=w.left,w.element.scrollTop=w.top}Wl=!!Rs,Ns=Rs=null,e.current=n,Wp=r;do{try{for(y=e;null!==Wp;){var E=Wp.flags;if(36&E&&sp(y,Wp.alternate,Wp),128&E){b=void 0;var S=Wp.ref;if(null!==S){var D=Wp.stateNode;Wp.tag,b=D,"function"==typeof S?S(b):S.current=b}}Wp=Wp.nextEffect}}catch(e){if(null===Wp)throw Error(Zo(330));Rv(Wp,e),Wp=Wp.nextEffect}}while(null!==Wp);Wp=null,Cc(),Dp=o}else e.current=n;if($p)$p=!1,Yp=e,qp=t;else for(Wp=r;null!==Wp;)t=Wp.nextEffect,Wp.nextEffect=null,8&Wp.flags&&((E=Wp).sibling=null,E.stateNode=null),Wp=t;if(0===(r=e.pendingLanes)&&(Bp=null),1===r?e===Jp?Xp++:(Xp=0,Jp=e):Xp=0,n=n.stateNode,hc&&"function"==typeof hc.onCommitFiberRoot)try{hc.onCommitFiberRoot(vc,n,void 0,!(64&~n.current.flags))}catch(e){}if(uv(e,zc()),Hp)throw Hp=!1,e=Vp,Vp=null,e;return 8&Dp||Ic(),null}function xv(){for(;null!==Wp;){var e=Wp.alternate;rv||null===nv||(8&Wp.flags?Yi(Wp,nv)&&(rv=!0):13===Wp.tag&&wp(e,Wp)&&Yi(Wp,nv)&&(rv=!0));var t=Wp.flags;256&t&&up(e,Wp),!(512&t)||$p||($p=!0,Fc(97,(function(){return Ov(),null}))),Wp=Wp.nextEffect}}function Ov(){if(90!==qp){var e=97<qp?97:qp;return qp=90,jc(e,Tv)}return!1}function Mv(e,t){Kp.push(t,e),$p||($p=!0,Fc(97,(function(){return Ov(),null})))}function Cv(e,t){Qp.push(t,e),$p||($p=!0,Fc(97,(function(){return Ov(),null})))}function Tv(){if(null===Yp)return!1;var e=Yp;if(Yp=null,48&Dp)throw Error(Zo(331));var t=Dp;Dp|=32;var n=Qp;Qp=[];for(var r=0;r<n.length;r+=2){var o=n[r],a=n[r+1],i=o.destroy;if(o.destroy=void 0,"function"==typeof i)try{i()}catch(e){if(null===a)throw Error(Zo(330));Rv(a,e)}}for(n=Kp,Kp=[],r=0;r<n.length;r+=2){o=n[r],a=n[r+1];try{var l=o.create;o.destroy=l()}catch(e){if(null===a)throw Error(Zo(330));Rv(a,e)}}for(l=e.current.firstEffect;null!==l;)e=l.nextEffect,l.nextEffect=null,8&l.flags&&(l.sibling=null,l.stateNode=null),l=e;return Dp=t,Ic(),!0}function Pv(e,t,n){nf(e,t=op(0,t=tp(n,t),1)),t=ov(),null!==(e=lv(e,1))&&(Ll(e,1,t),uv(e,t))}function Rv(e,t){if(3===e.tag)Pv(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){Pv(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Bp||!Bp.has(r))){var o=ap(n,e=tp(t,e),1);if(nf(n,o),o=ov(),null!==(n=lv(n,1)))Ll(n,1,o),uv(n,o);else if("function"==typeof r.componentDidCatch&&(null===Bp||!Bp.has(r)))try{r.componentDidCatch(t,e)}catch(e){}break}}n=n.return}}function Nv(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ov(),e.pingedLanes|=e.suspendedLanes&n,_p===e&&(Op&n)===n&&(4===Tp||3===Tp&&(62914560&Op)===Op&&500>zc()-jp?mv(e,0):Lp|=n),uv(e,t)}function zv(e,t){var n=e.stateNode;null!==n&&n.delete(t),0===(t=0)&&(2&(t=e.mode)?4&t?(0===ev&&(ev=Rp),0===(t=Nl(62914560&~ev))&&(t=4194304)):t=99===Lc()?1:2:t=1),n=ov(),null!==(e=lv(e,t))&&(Ll(e,t,n),uv(e,n))}function Lv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.flags=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childLanes=this.lanes=0,this.alternate=null}function Av(e,t,n,r){return new Lv(e,t,n,r)}function jv(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Fv(e,t){var n=e.alternate;return null===n?((n=Av(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Iv(e,t,n,r,o,a){var i=2;if(r=e,"function"==typeof e)jv(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case ga:return Uv(n.children,o,a,t);case Ca:i=8,o|=16;break;case ya:i=8,o|=1;break;case ba:return(e=Av(12,n,t,8|o)).elementType=ba,e.type=ba,e.lanes=a,e;case Sa:return(e=Av(13,n,t,o)).type=Sa,e.elementType=Sa,e.lanes=a,e;case Da:return(e=Av(19,n,t,o)).elementType=Da,e.lanes=a,e;case Ta:return Wv(n,o,a,t);case Pa:return(e=Av(24,n,t,o)).elementType=Pa,e.lanes=a,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case wa:i=10;break e;case ka:i=9;break e;case Ea:i=11;break e;case _a:i=14;break e;case xa:i=16,r=null;break e;case Oa:i=22;break e}throw Error(Zo(130,null==e?e:typeof e,""))}return(t=Av(i,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function Uv(e,t,n,r){return(e=Av(7,e,r,t)).lanes=n,e}function Wv(e,t,n,r){return(e=Av(23,e,r,t)).elementType=Ta,e.lanes=n,e}function Hv(e,t,n){return(e=Av(6,e,null,t)).lanes=n,e}function Vv(e,t,n){return(t=Av(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bv(e,t,n){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=0,this.eventTimes=zl(0),this.expirationTimes=zl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=zl(0),this.mutableSourceEagerHydrationData=null}function $v(e,t,n,r){var o=t.current,a=ov(),i=av(o);e:if(n){t:{if(Hi(n=n._reactInternals)!==n||1!==n.tag)throw Error(Zo(170));var l=n;do{switch(l.tag){case 3:l=l.stateNode.context;break t;case 1:if(uc(l.type)){l=l.stateNode.__reactInternalMemoizedMergedChildContext;break t}}l=l.return}while(null!==l);throw Error(Zo(171))}if(1===n.tag){var u=n.type;if(uc(u)){n=fc(n,u,l);break e}}n=l}else n=rc;return null===t.context?t.context=n:t.pendingContext=n,(t=tf(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),nf(o,t),iv(o,i,a),i}function Yv(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qv(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Kv(e,t){qv(e,t),(e=e.alternate)&&qv(e,t)}function Qv(e,t,n){var r=null!=n&&null!=n.hydrationOptions&&n.hydrationOptions.mutableSources||null;if(n=new Bv(e,t,null!=n&&!0===n.hydrate),t=Av(3,null,null,2===t?7:1===t?3:0),n.current=t,t.stateNode=n,Zc(t),e[$s]=n.current,Ss(8===e.nodeType?e.parentNode:e),r)for(e=0;e<r.length;e++){var o=(t=r[e])._getVersion;o=o(t._source),null==n.mutableSourceEagerHydrationData?n.mutableSourceEagerHydrationData=[t,o]:n.mutableSourceEagerHydrationData.push(t,o)}this._internalRoot=n}function Gv(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xv(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a._internalRoot;if("function"==typeof o){var l=o;o=function(){var e=Yv(i);l.call(e)}}$v(t,i,e,o)}else{if(a=n._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var n;n=e.lastChild;)e.removeChild(n);return new Qv(e,0,t?{hydrate:!0}:void 0)}(n,r),i=a._internalRoot,"function"==typeof o){var u=o;o=function(){var e=Yv(i);u.call(e)}}pv((function(){$v(t,i,e,o)}))}return Yv(i)}function Jv(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gv(t))throw Error(Zo(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:ma,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)}Up=function(e,t,n){var r=t.lanes;if(null!==e)if(e.memoizedProps!==t.pendingProps||ac.current)Md=!0;else{if(!(n&r)){switch(Md=!1,t.tag){case 3:Fd(t),If();break;case 5:Of(t);break;case 1:uc(t.type)&&dc(t);break;case 4:_f(t,t.stateNode.containerInfo);break;case 10:r=t.memoizedProps.value;var o=t.type._context;nc(Vc,o._currentValue),o._currentValue=r;break;case 13:if(null!==t.memoizedState)return n&t.child.childLanes?Bd(e,t,n):(nc(Cf,1&Cf.current),null!==(t=Xd(e,t,n))?t.sibling:null);nc(Cf,1&Cf.current);break;case 19:if(r=!!(n&t.childLanes),64&e.flags){if(r)return Gd(e,t,n);t.flags|=64}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),nc(Cf,Cf.current),r)break;return null;case 23:case 24:return t.lanes=0,Nd(e,t,n)}return Xd(e,t,n)}Md=!!(16384&e.flags)}else Md=!1;switch(t.lanes=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,o=lc(t,oc.current),Gc(t,n),o=Jf(null,t,r,e,o,n),t.flags|=1,"object"==typeof o&&null!==o&&"function"==typeof o.render&&void 0===o.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,uc(r)){var a=!0;dc(t)}else a=!1;t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Zc(t);var i=r.getDerivedStateFromProps;"function"==typeof i&&uf(t,r,i,e),o.updater=sf,t.stateNode=o,o._reactInternals=t,pf(t,r,e,n),t=jd(null,t,r,!0,a,n)}else t.tag=0,Cd(null,t,o,n),t=t.child;return t;case 16:o=t.elementType;e:{switch(null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,o=(a=o._init)(o._payload),t.type=o,a=t.tag=function(e){if("function"==typeof e)return jv(e)?1:0;if(null!=e){if((e=e.$$typeof)===Ea)return 11;if(e===_a)return 14}return 2}(o),e=Hc(o,e),a){case 0:t=Ld(null,t,o,e,n);break e;case 1:t=Ad(null,t,o,e,n);break e;case 11:t=Td(null,t,o,e,n);break e;case 14:t=Pd(null,t,o,Hc(o.type,e),r,n);break e}throw Error(Zo(306,o,""))}return t;case 0:return r=t.type,o=t.pendingProps,Ld(e,t,r,o=t.elementType===r?o:Hc(r,o),n);case 1:return r=t.type,o=t.pendingProps,Ad(e,t,r,o=t.elementType===r?o:Hc(r,o),n);case 3:if(Fd(t),r=t.updateQueue,null===e||null===r)throw Error(Zo(282));if(r=t.pendingProps,o=null!==(o=t.memoizedState)?o.element:null,ef(e,t),of(t,r,null,n),(r=t.memoizedState.element)===o)If(),t=Xd(e,t,n);else{if((a=(o=t.stateNode).hydrate)&&(Rf=Is(t.stateNode.containerInfo.firstChild),Pf=t,a=Nf=!0),a){if(null!=(e=o.mutableSourceEagerHydrationData))for(o=0;o<e.length;o+=2)(a=e[o])._workInProgressVersionPrimary=e[o+1],Uf.push(a);for(n=bf(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|1024,n=n.sibling}else Cd(e,t,r,n),If();t=t.child}return t;case 5:return Of(t),null===e&&Af(t),r=t.type,o=t.pendingProps,a=null!==e?e.memoizedProps:null,i=o.children,Ls(r,o)?i=null:null!==a&&Ls(r,a)&&(t.flags|=16),zd(e,t),Cd(e,t,i,n),t.child;case 6:return null===e&&Af(t),null;case 13:return Bd(e,t,n);case 4:return _f(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=yf(t,null,r,n):Cd(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,Td(e,t,r,o=t.elementType===r?o:Hc(r,o),n);case 7:return Cd(e,t,t.pendingProps,n),t.child;case 8:case 12:return Cd(e,t,t.pendingProps.children,n),t.child;case 10:e:{r=t.type._context,o=t.pendingProps,i=t.memoizedProps,a=o.value;var l=t.type._context;if(nc(Vc,l._currentValue),l._currentValue=a,null!==i)if(l=i.value,0===(a=ts(l,a)?0:0|("function"==typeof r._calculateChangedBits?r._calculateChangedBits(l,a):**********))){if(i.children===o.children&&!ac.current){t=Xd(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var u=l.dependencies;if(null!==u){i=l.child;for(var s=u.firstContext;null!==s;){if(s.context===r&&s.observedBits&a){1===l.tag&&((s=tf(-1,n&-n)).tag=2,nf(l,s)),l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Qc(l.return,n),u.lanes|=n;break}s=s.next}}else i=10===l.tag&&l.type===t.type?null:l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}Cd(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=(a=t.pendingProps).children,Gc(t,n),r=r(o=Xc(o,a.unstable_observedBits)),t.flags|=1,Cd(e,t,r,n),t.child;case 14:return a=Hc(o=t.type,t.pendingProps),Pd(e,t,o,a=Hc(o.type,a),r,n);case 15:return Rd(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Hc(r,o),null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),t.tag=1,uc(r)?(e=!0,dc(t)):e=!1,Gc(t,n),ff(t,r,o),pf(t,r,o,n),jd(null,t,r,!0,e,n);case 19:return Gd(e,t,n);case 23:case 24:return Nd(e,t,n)}throw Error(Zo(156,t.tag))},Qv.prototype.render=function(e){$v(e,this._internalRoot,null,null)},Qv.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;$v(null,e,null,(function(){t[$s]=null}))},qi=function(e){13===e.tag&&(iv(e,4,ov()),Kv(e,4))},Ki=function(e){13===e.tag&&(iv(e,67108864,ov()),Kv(e,67108864))},Qi=function(e){if(13===e.tag){var t=ov(),n=av(e);iv(e,n,t),Kv(e,n)}},Gi=function(e,t){return t()},bi=function(e,t,n){switch(t){case"input":if(Qa(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Gs(r);if(!o)throw Error(Zo(90));Ba(r),Qa(r,o)}}}break;case"textarea":ni(e,n);break;case"select":null!=(t=n.value)&&Za(e,!!n.multiple,t,!1)}},_i=dv,xi=function(e,t,n,r,o){var a=Dp;Dp|=4;try{return jc(98,e.bind(null,t,n,r,o))}finally{0===(Dp=a)&&(Ip(),Ic())}},Oi=function(){!(49&Dp)&&(function(){if(null!==Gp){var e=Gp;Gp=null,e.forEach((function(e){e.expiredLanes|=24&e.pendingLanes,uv(e,zc())}))}Ic()}(),Ov())},Mi=function(e,t){var n=Dp;Dp|=2;try{return e(t)}finally{0===(Dp=n)&&(Ip(),Ic())}};var Zv={Events:[Ks,Qs,Gs,Si,Di,Ov,{current:!1}]},eh={findFiberByHostInstance:qs,bundleType:0,version:"17.0.2",rendererPackageName:"react-dom"},th={bundleType:eh.bundleType,version:eh.version,rendererPackageName:eh.rendererPackageName,rendererConfig:eh.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:va.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$i(e))?null:e.stateNode},findFiberByHostInstance:eh.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var nh=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!nh.isDisabled&&nh.supportsFiber)try{vc=nh.inject(th),hc=nh}catch(ui){}}Yo.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zv,Yo.createPortal=Jv,Yo.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(Zo(188));throw Error(Zo(268,Object.keys(e)))}return e=null===(e=$i(t))?null:e.stateNode},Yo.flushSync=function(e,t){var n=Dp;if(48&n)return e(t);Dp|=1;try{if(e)return jc(99,e.bind(null,t))}finally{Dp=n,Ic()}},Yo.hydrate=function(e,t,n){if(!Gv(t))throw Error(Zo(200));return Xv(null,e,t,!0,n)},Yo.render=function(e,t,n){if(!Gv(t))throw Error(Zo(200));return Xv(null,e,t,!1,n)},Yo.unmountComponentAtNode=function(e){if(!Gv(e))throw Error(Zo(40));return!!e._reactRootContainer&&(pv((function(){Xv(null,null,e,!1,(function(){e._reactRootContainer=null,e[$s]=null}))})),!0)},Yo.unstable_batchedUpdates=dv,Yo.unstable_createPortal=function(e,t){return Jv(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},Yo.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gv(n))throw Error(Zo(200));if(null==e||void 0===e._reactInternals)throw Error(Zo(38));return Xv(e,t,n,!1,r)},Yo.version="17.0.2",function(e){!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=Yo}($o);var rh=x(Bo);var oh=function(e){return Bn(function(e){return e&&"setState"in e?rh.findDOMNode(e):null!=e?e:null}(e))},ah=function(){};var ih,lh=function(e){return e&&("current"in e?e.current:e)};function uh(e){var t,n,r,o,a,i=e.enabled,l=e.enableEvents,u=e.placement,s=e.flip,c=e.offset,f=e.fixed,d=e.containerPadding,p=e.arrowElement,v=e.popperConfig,h=void 0===v?{}:v,m=function(e){var t={};return Array.isArray(e)?(null==e||e.forEach((function(e){t[e.name]=e})),t):e||t}(h.modifiers);return fe({},h,{placement:u,enabled:i,strategy:f?"fixed":h.strategy,modifiers:(a=fe({},m,{eventListeners:{enabled:l},preventOverflow:fe({},m.preventOverflow,{options:d?fe({padding:d},null==(t=m.preventOverflow)?void 0:t.options):null==(n=m.preventOverflow)?void 0:n.options}),offset:{options:fe({offset:c},null==(r=m.offset)?void 0:r.options)},arrow:fe({},m.arrow,{enabled:!!p,options:fe({},null==(o=m.arrow)?void 0:o.options,{element:p})}),flip:fe({enabled:!!s},m.flip)}),void 0===a&&(a={}),Array.isArray(a)?a:Object.keys(a).map((function(e){return a[e].name=e,a[e]})))})}function sh(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function ch(e){if((!ih&&0!==ih||e)&&ar){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),ih=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return ih}var fh=function(e){var t;return"undefined"==typeof document?null:null==e?Bn().body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),null!=(t=e)&&t.nodeType&&e||null)};function dh(e,t){var n=O.useState((function(){return fh(e)})),r=n[0],o=n[1];if(!r){var a=fh(e);a&&o(a)}return O.useEffect((function(){t&&r&&t(r)}),[t,r]),O.useEffect((function(){var t=fh(e);t!==r&&o(t)}),[e,r]),r}const ph=e=>e&&"function"!=typeof e?t=>{e.current=t}:e;function vh(e,t){return O.useMemo((()=>function(e,t){const n=ph(e),r=ph(t);return e=>{n&&n(e),r&&r(e)}}(e,t)),[e,t])}var hh=ce.forwardRef((function(e,t){var n=e.flip,r=e.offset,o=e.placement,a=e.containerPadding,i=void 0===a?5:a,u=e.popperConfig,s=void 0===u?{}:u,c=e.transition,f=br(),d=f[0],p=f[1],v=br(),h=v[0],m=v[1],g=vh(p,t),y=dh(e.container),b=dh(e.target),w=O.useState(!e.show),k=w[0],E=w[1],S=function(e,t,n){var r=void 0===n?{}:n,o=r.enabled,a=void 0===o||o,i=r.placement,u=void 0===i?"bottom":i,s=r.strategy,c=void 0===s?"absolute":s,f=r.modifiers,d=void 0===f?Ho:f,p=l(r,["enabled","placement","strategy","modifiers"]),v=O.useRef(),h=O.useCallback((function(){var e;null==(e=v.current)||e.update()}),[]),m=O.useCallback((function(){var e;null==(e=v.current)||e.forceUpdate()}),[]),g=wr(O.useState({placement:u,update:h,forceUpdate:m,attributes:{},styles:{popper:Io(c),arrow:{}}})),y=g[0],b=g[1],w=O.useMemo((function(){return{name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:function(e){var t=e.state,n={},r={};Object.keys(t.elements).forEach((function(e){n[e]=t.styles[e],r[e]=t.attributes[e]})),b({state:t,styles:n,attributes:r,update:h,forceUpdate:m,placement:t.placement})}}}),[h,m,b]);return O.useEffect((function(){v.current&&a&&v.current.setOptions({placement:u,strategy:c,modifiers:[].concat(d,[w,Uo])})}),[c,u,w,a]),O.useEffect((function(){if(a&&null!=e&&null!=t)return v.current=Fo(e,t,fe({},p,{placement:u,strategy:c,modifiers:[].concat(d,[Wo,w])})),function(){null!=v.current&&(v.current.destroy(),v.current=void 0,b((function(e){return fe({},e,{attributes:{},styles:{popper:Io(c)}})})))}}),[a,e,t]),y}(b,d,uh({placement:o,enableEvents:!!e.show,containerPadding:i||5,flip:n,offset:r,arrowElement:h,popperConfig:s})),D=S.styles,_=S.attributes,x=l(S,["styles","attributes"]);e.show?k&&E(!1):e.transition||k||E(!0);var M,C,T,P,R,N,z,L,A,j,F,I,U=e.show||c&&!k;if(M=d,C=e.onHide,T={disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent},R=(P=void 0===T?{}:T).disabled,N=P.clickTrigger,z=void 0===N?"click":N,L=O.useRef(!1),A=C||ah,j=O.useCallback((function(e){var t,n,r=lh(M);L.current=!(r&&(n=e,!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey))&&function(e){return 0===e.button}(e)&&!Xn(r,null!=(t=null==e.composedPath?void 0:e.composedPath()[0])?t:e.target))}),[M]),F=yr((function(e){L.current||A(e)})),I=yr((function(e){27===e.keyCode&&A(e)})),O.useEffect((function(){if(!R&&null!=M){var e=window.event,t=oh(lh(M)),n=Vo(t,z,j,!0),r=Vo(t,z,(function(t){t!==e?F(t):e=void 0})),o=Vo(t,"keyup",(function(t){t!==e?I(t):e=void 0})),a=[];return"ontouchstart"in t.documentElement&&(a=[].slice.call(t.body.children).map((function(e){return Vo(e,"mousemove",ah)}))),function(){n(),r(),o(),a.forEach((function(e){return e()}))}}}),[M,R,z,j,F,I]),!U)return null;var W=e.children(fe({},x,{show:!!e.show,props:fe({},_.popper,{style:D.popper,ref:g}),arrowProps:fe({},_.arrow,{style:D.arrow,ref:m})}));if(c){var H=e.onExit,V=e.onExiting,B=e.onEnter,$=e.onEntering,Y=e.onEntered;W=ce.createElement(c,{in:e.show,appear:!0,onExit:H,onExiting:V,onExited:function(){E(!0),e.onExited&&e.onExited.apply(e,arguments)},onEnter:B,onEntering:$,onEntered:Y},W)}return y?rh.createPortal(W,y):null}));hh.displayName="Overlay",hh.propTypes={show:be.bool,placement:be.oneOf(zr),target:be.any,container:be.any,flip:be.bool,children:be.func.isRequired,containerPadding:be.number,popperConfig:be.object,rootClose:be.bool,rootCloseEvent:be.oneOf(["click","mousedown"]),rootCloseDisabled:be.bool,onHide:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o;return e.rootClose?(o=be.func).isRequired.apply(o,[e].concat(n)):be.func.apply(be,[e].concat(n))},transition:be.elementType,onEnter:be.func,onEntering:be.func,onEntered:be.func,onExit:be.func,onExiting:be.func,onExited:be.func};var mh=hh;var gh=["style","className","event","selected","isAllDay","onSelect","onDoubleClick","onKeyPress","localizer","continuesPrior","continuesAfter","accessors","getters","children","components","slotStart","slotEnd"],yh=function(e){function t(){return s(this,t),h(this,t,arguments)}return g(t,e),f(t,[{key:"render",value:function(){var e=this.props,t=e.style,n=e.className,r=e.event,o=e.selected,a=e.isAllDay,l=e.onSelect,s=e.onDoubleClick,c=e.onKeyPress,f=e.localizer,d=e.continuesPrior,p=e.continuesAfter,v=e.accessors,h=e.getters,m=e.children,g=e.components,y=g.event,b=g.eventWrapper,w=e.slotStart,k=e.slotEnd,E=u(e,gh);delete E.resizable;var S=v.title(r),_=v.tooltip(r),x=v.end(r),O=v.start(r),M=v.allDay(r),C=a||M||f.diff(O,f.ceil(x,"day"),"day")>1,T=h.eventProp(r,O,x,o),P=ce.createElement("div",{className:"rbc-event-content",title:_||void 0},y?ce.createElement(y,{event:r,continuesPrior:d,continuesAfter:p,title:S,isAllDay:M,localizer:f,slotStart:w,slotEnd:k}):S);return ce.createElement(b,Object.assign({},this.props,{type:"date"}),ce.createElement("div",Object.assign({},E,{style:i(i({},T.style),t),className:D("rbc-event",n,T.className,{"rbc-selected":o,"rbc-event-allday":C,"rbc-event-continues-prior":d,"rbc-event-continues-after":p}),onClick:function(e){return l&&l(r,e)},onDoubleClick:function(e){return s&&s(r,e)},onKeyDown:function(e){return c&&c(r,e)}}),"function"==typeof m?m(P):P))}}])}(ce.Component);var bh=function(){this.__data__=[],this.size=0},wh=Ut;var kh=function(e,t){for(var n=e.length;n--;)if(wh(e[n][0],t))return n;return-1},Eh=kh,Sh=Array.prototype.splice;var Dh=kh;var _h=kh;var xh=kh;var Oh=bh,Mh=function(e){var t=this.__data__,n=Eh(t,e);return!(n<0)&&(n==t.length-1?t.pop():Sh.call(t,n,1),--this.size,!0)},Ch=function(e){var t=this.__data__,n=Dh(t,e);return n<0?void 0:t[n][1]},Th=function(e){return _h(this.__data__,e)>-1},Ph=function(e,t){var n=this.__data__,r=xh(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function Rh(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Rh.prototype.clear=Oh,Rh.prototype.delete=Mh,Rh.prototype.get=Ch,Rh.prototype.has=Th,Rh.prototype.set=Ph;var Nh=Rh,zh=Nh;var Lh=function(){this.__data__=new zh,this.size=0};var Ah=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};var jh=function(e){return this.__data__.get(e)};var Fh,Ih=function(e){return this.__data__.has(e)},Uh=Bt["__core-js_shared__"],Wh=(Fh=/[^.]+$/.exec(Uh&&Uh.keys&&Uh.keys.IE_PROTO||""))?"Symbol(src)_1."+Fh:"";var Hh=function(e){return!!Wh&&Wh in e},Vh=Function.prototype.toString;var Bh=function(e){if(null!=e){try{return Vh.call(e)}catch(e){}try{return e+""}catch(e){}}return""},$h=ln,Yh=Hh,qh=rn,Kh=Bh,Qh=/^\[object .+?Constructor\]$/,Gh=Function.prototype,Xh=Object.prototype,Jh=Gh.toString,Zh=Xh.hasOwnProperty,em=RegExp("^"+Jh.call(Zh).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var tm=function(e){return!(!qh(e)||Yh(e))&&($h(e)?em:Qh).test(Kh(e))},nm=function(e,t){return null==e?void 0:e[t]};var rm=function(e,t){var n=nm(e,t);return tm(n)?n:void 0},om=rm(Bt,"Map"),am=rm(Object,"create"),im=am;var lm=function(){this.__data__=im?im(null):{},this.size=0};var um=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},sm=am,cm=Object.prototype.hasOwnProperty;var fm=function(e){var t=this.__data__;if(sm){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return cm.call(t,e)?t[e]:void 0},dm=am,pm=Object.prototype.hasOwnProperty;var vm=am;var hm=lm,mm=um,gm=fm,ym=function(e){var t=this.__data__;return dm?void 0!==t[e]:pm.call(t,e)},bm=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=vm&&void 0===t?"__lodash_hash_undefined__":t,this};function wm(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}wm.prototype.clear=hm,wm.prototype.delete=mm,wm.prototype.get=gm,wm.prototype.has=ym,wm.prototype.set=bm;var km=wm,Em=Nh,Sm=om;var Dm=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var _m=function(e,t){var n=e.__data__;return Dm(t)?n["string"==typeof t?"string":"hash"]:n.map},xm=_m;var Om=_m;var Mm=_m;var Cm=_m;var Tm=function(e,t){var n=Cm(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},Pm=function(){this.size=0,this.__data__={hash:new km,map:new(Sm||Em),string:new km}},Rm=function(e){var t=xm(this,e).delete(e);return this.size-=t?1:0,t},Nm=function(e){return Om(this,e).get(e)},zm=function(e){return Mm(this,e).has(e)},Lm=Tm;function Am(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Am.prototype.clear=Pm,Am.prototype.delete=Rm,Am.prototype.get=Nm,Am.prototype.has=zm,Am.prototype.set=Lm;var jm=Am,Fm=Nh,Im=om,Um=jm;var Wm=Nh,Hm=Lh,Vm=Ah,Bm=jh,$m=Ih,Ym=function(e,t){var n=this.__data__;if(n instanceof Fm){var r=n.__data__;if(!Im||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Um(r)}return n.set(e,t),this.size=n.size,this};function qm(e){var t=this.__data__=new Wm(e);this.size=t.size}qm.prototype.clear=Hm,qm.prototype.delete=Vm,qm.prototype.get=Bm,qm.prototype.has=$m,qm.prototype.set=Ym;var Km=qm;var Qm=jm,Gm=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},Xm=function(e){return this.__data__.has(e)};function Jm(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Qm;++t<n;)this.add(e[t])}Jm.prototype.add=Jm.prototype.push=Gm,Jm.prototype.has=Xm;var Zm=Jm,eg=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1},tg=function(e,t){return e.has(t)};var ng=function(e,t,n,r,o,a){var i=1&n,l=e.length,u=t.length;if(l!=u&&!(i&&u>l))return!1;var s=a.get(e),c=a.get(t);if(s&&c)return s==t&&c==e;var f=-1,d=!0,p=2&n?new Zm:void 0;for(a.set(e,t),a.set(t,e);++f<l;){var v=e[f],h=t[f];if(r)var m=i?r(h,v,f,t,e,a):r(v,h,f,e,t,a);if(void 0!==m){if(m)continue;d=!1;break}if(p){if(!eg(t,(function(e,t){if(!tg(p,t)&&(v===e||o(v,e,n,r,a)))return p.push(t)}))){d=!1;break}}else if(v!==h&&!o(v,h,n,r,a)){d=!1;break}}return a.delete(e),a.delete(t),d},rg=Bt.Uint8Array;var og=rg,ag=Ut,ig=ng,lg=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n},ug=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n},sg=$t?$t.prototype:void 0,cg=sg?sg.valueOf:void 0;var fg=function(e,t,n,r,o,a,i){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!a(new og(e),new og(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return ag(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var l=lg;case"[object Set]":var u=1&r;if(l||(l=ug),e.size!=t.size&&!u)return!1;var s=i.get(e);if(s)return s==t;r|=2,i.set(e,t);var c=ig(l(e),l(t),r,o,a,i);return i.delete(e),c;case"[object Symbol]":if(cg)return cg.call(e)==cg.call(t)}return!1};var dg=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e},pg=Array.isArray,vg=dg,hg=pg;var mg=function(e,t,n){var r=t(e);return hg(e)?r:vg(r,n(e))};var gg=function(){return[]},yg=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a},bg=gg,wg=Object.prototype.propertyIsEnumerable,kg=Object.getOwnPropertySymbols,Eg=kg?function(e){return null==e?[]:(e=Object(e),yg(kg(e),(function(t){return wg.call(e,t)})))}:bg;var Sg=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},Dg=nn,_g=En;var xg=function(e){return _g(e)&&"[object Arguments]"==Dg(e)},Og=En,Mg=Object.prototype,Cg=Mg.hasOwnProperty,Tg=Mg.propertyIsEnumerable,Pg=xg(function(){return arguments}())?xg:function(e){return Og(e)&&Cg.call(e,"callee")&&!Tg.call(e,"callee")},Rg=Pg,Ng={},zg={get exports(){return Ng},set exports(e){Ng=e}};var Lg=function(){return!1};!function(e,t){var n=Bt,r=Lg,o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o?n.Buffer:void 0,l=(i?i.isBuffer:void 0)||r;e.exports=l}(zg,Ng);var Ag=nn,jg=un,Fg=En,Ig={};Ig["[object Float32Array]"]=Ig["[object Float64Array]"]=Ig["[object Int8Array]"]=Ig["[object Int16Array]"]=Ig["[object Int32Array]"]=Ig["[object Uint8Array]"]=Ig["[object Uint8ClampedArray]"]=Ig["[object Uint16Array]"]=Ig["[object Uint32Array]"]=!0,Ig["[object Arguments]"]=Ig["[object Array]"]=Ig["[object ArrayBuffer]"]=Ig["[object Boolean]"]=Ig["[object DataView]"]=Ig["[object Date]"]=Ig["[object Error]"]=Ig["[object Function]"]=Ig["[object Map]"]=Ig["[object Number]"]=Ig["[object Object]"]=Ig["[object RegExp]"]=Ig["[object Set]"]=Ig["[object String]"]=Ig["[object WeakMap]"]=!1;var Ug=function(e){return Fg(e)&&jg(e.length)&&!!Ig[Ag(e)]};var Wg=function(e){return function(t){return e(t)}},Hg={},Vg={get exports(){return Hg},set exports(e){Hg=e}};!function(e,t){var n=Wt,r=t&&!t.nodeType&&t,o=r&&e&&!e.nodeType&&e,a=o&&o.exports===r&&n.process,i=function(){try{var e=o&&o.require&&o.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=i}(Vg,Hg);var Bg=Ug,$g=Wg,Yg=Hg&&Hg.isTypedArray,qg=Yg?$g(Yg):Bg,Kg=Sg,Qg=Rg,Gg=pg,Xg=Ng,Jg=pn,Zg=qg,ey=Object.prototype.hasOwnProperty;var ty=function(e,t){var n=Gg(e),r=!n&&Qg(e),o=!n&&!r&&Xg(e),a=!n&&!r&&!o&&Zg(e),i=n||r||o||a,l=i?Kg(e.length,String):[],u=l.length;for(var s in e)!t&&!ey.call(e,s)||i&&("length"==s||o&&("offset"==s||"parent"==s)||a&&("buffer"==s||"byteLength"==s||"byteOffset"==s)||Jg(s,u))||l.push(s);return l},ny=Object.prototype;var ry=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||ny)};var oy=function(e,t){return function(n){return e(t(n))}},ay=oy(Object.keys,Object),iy=ry,ly=ay,uy=Object.prototype.hasOwnProperty;var sy=ty,cy=function(e){if(!iy(e))return ly(e);var t=[];for(var n in Object(e))uy.call(e,n)&&"constructor"!=n&&t.push(n);return t},fy=fn;var dy=function(e){return fy(e)?sy(e):cy(e)},py=mg,vy=Eg,hy=dy;var my=function(e){return py(e,hy,vy)},gy=my,yy=Object.prototype.hasOwnProperty;var by=function(e,t,n,r,o,a){var i=1&n,l=gy(e),u=l.length;if(u!=gy(t).length&&!i)return!1;for(var s=u;s--;){var c=l[s];if(!(i?c in t:yy.call(t,c)))return!1}var f=a.get(e),d=a.get(t);if(f&&d)return f==t&&d==e;var p=!0;a.set(e,t),a.set(t,e);for(var v=i;++s<u;){var h=e[c=l[s]],m=t[c];if(r)var g=i?r(m,h,c,t,e,a):r(h,m,c,e,t,a);if(!(void 0===g?h===m||o(h,m,n,r,a):g)){p=!1;break}v||(v="constructor"==c)}if(p&&!v){var y=e.constructor,b=t.constructor;y==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b||(p=!1)}return a.delete(e),a.delete(t),p},wy=rm(Bt,"DataView"),ky=om,Ey=rm(Bt,"Promise"),Sy=rm(Bt,"Set"),Dy=rm(Bt,"WeakMap"),_y=nn,xy=Bh,Oy="[object Map]",My="[object Promise]",Cy="[object Set]",Ty="[object WeakMap]",Py="[object DataView]",Ry=xy(wy),Ny=xy(ky),zy=xy(Ey),Ly=xy(Sy),Ay=xy(Dy),jy=_y;(wy&&jy(new wy(new ArrayBuffer(1)))!=Py||ky&&jy(new ky)!=Oy||Ey&&jy(Ey.resolve())!=My||Sy&&jy(new Sy)!=Cy||Dy&&jy(new Dy)!=Ty)&&(jy=function(e){var t=_y(e),n="[object Object]"==t?e.constructor:void 0,r=n?xy(n):"";if(r)switch(r){case Ry:return Py;case Ny:return Oy;case zy:return My;case Ly:return Cy;case Ay:return Ty}return t});var Fy=jy,Iy=Km,Uy=ng,Wy=fg,Hy=by,Vy=Fy,By=pg,$y=Ng,Yy=qg,qy="[object Arguments]",Ky="[object Array]",Qy="[object Object]",Gy=Object.prototype.hasOwnProperty;var Xy=function(e,t,n,r,o,a){var i=By(e),l=By(t),u=i?Ky:Vy(e),s=l?Ky:Vy(t),c=(u=u==qy?Qy:u)==Qy,f=(s=s==qy?Qy:s)==Qy,d=u==s;if(d&&$y(e)){if(!$y(t))return!1;i=!0,c=!1}if(d&&!c)return a||(a=new Iy),i||Yy(e)?Uy(e,t,n,r,o,a):Wy(e,t,u,n,r,o,a);if(!(1&n)){var p=c&&Gy.call(e,"__wrapped__"),v=f&&Gy.call(t,"__wrapped__");if(p||v){var h=p?e.value():e,m=v?t.value():t;return a||(a=new Iy),o(h,m,n,r,a)}}return!!d&&(a||(a=new Iy),Hy(e,t,n,r,o,a))},Jy=En;var Zy=function e(t,n,r,o,a){return t===n||(null==t||null==n||!Jy(t)&&!Jy(n)?t!=t&&n!=n:Xy(t,n,r,o,e,a))},eb=Zy;var tb=function(e,t){return eb(e,t)};function nb(e,t){return!(!e||null==t)&&tb(e,t)}function rb(e,t){return(e.right-e.left)/t}function ob(e,t,n,r){var o=rb(e,r);return n?r-1-Math.floor((t-e.left)/o):Math.floor((t-e.left)/o)}function ab(e){var t,n,r,o=e.containerRef,a=e.accessors,i=e.getters,l=e.selected,u=e.components,s=e.localizer,c=e.position,f=e.show,d=e.events,p=e.slotStart,v=e.slotEnd,h=e.onSelect,m=e.onDoubleClick,g=e.onKeyPress,y=e.handleDragStart,b=e.popperRef,w=e.target,k=e.offset;n=(t={ref:b,callback:f}).ref,r=t.callback,O.useEffect((function(){var e=function(e){n.current&&!n.current.contains(e.target)&&r()};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}}),[n,r]),O.useLayoutEffect((function(){var e=function(e){var t=e.target,n=e.offset,r=e.container,o=e.box,a=nr(t),i=a.top,l=a.left,u=a.width,s=a.height,c=nr(r),f=c.top,d=c.left,p=c.width,v=c.height,h=nr(o),m=h.width,g=h.height,y=f+v,b=d+p,w=i+g,k=l+m,E=n.x,S=n.y;return{topOffset:w>y?i-g-S:i+S+s,leftOffset:k>b?l+E-m+u:l+E}}({target:w,offset:k,container:o.current,box:b.current}),t=e.topOffset,n=e.leftOffset;b.current.style.top="".concat(t,"px"),b.current.style.left="".concat(n,"px")}),[k.x,k.y,w]);var E=c.width,S={minWidth:E+E/2};return ce.createElement("div",{style:S,className:"rbc-overlay",ref:b},ce.createElement("div",{className:"rbc-overlay-header"},s.format(p,"dayHeaderFormat")),d.map((function(e,t){return ce.createElement(yh,{key:t,type:"popup",localizer:s,event:e,getters:i,onSelect:h,accessors:a,components:u,onDoubleClick:m,onKeyPress:g,continuesPrior:s.lt(a.end(e),p,"day"),continuesAfter:s.gte(a.start(e),v,"day"),slotStart:p,slotEnd:v,selected:nb(e,l),draggable:!0,onDragStart:function(){return y(e)},onDragEnd:function(){return f()}})})))}var ib=ce.forwardRef((function(e,t){return ce.createElement(ab,Object.assign({},e,{popperRef:t}))}));function lb(e){var t=e.containerRef,n=e.popupOffset,r=void 0===n?5:n,o=e.overlay,a=e.accessors,i=e.localizer,l=e.components,u=e.getters,s=e.selected,c=e.handleSelectEvent,f=e.handleDoubleClickEvent,d=e.handleKeyPressEvent,p=e.handleDragStart,v=e.onHide,h=e.overlayDisplay,m=O.useRef(null);if(!o.position)return null;var g=r;isNaN(r)||(g={x:r,y:r});var y=o.position,b=o.events,w=o.date,k=o.end;return ce.createElement(mh,{rootClose:!0,flip:!0,show:!0,placement:"bottom",onHide:v,target:o.target},(function(e){var n=e.props;return ce.createElement(ib,Object.assign({},n,{containerRef:t,ref:m,target:o.target,offset:g,accessors:a,getters:u,selected:s,components:l,localizer:i,position:y,show:h,events:b,slotStart:w,slotEnd:k,onSelect:c,onDoubleClick:f,onKeyPress:d,handleDragStart:p}))}))}ib.propTypes={accessors:be.object.isRequired,getters:be.object.isRequired,selected:be.object,components:be.object.isRequired,localizer:be.object.isRequired,position:be.object.isRequired,show:be.func.isRequired,events:be.array.isRequired,slotStart:be.instanceOf(Date).isRequired,slotEnd:be.instanceOf(Date),onSelect:be.func,onDoubleClick:be.func,onKeyPress:be.func,handleDragStart:be.func,style:be.object,offset:be.shape({x:be.number,y:be.number})};var ub=ce.forwardRef((function(e,t){return ce.createElement(lb,Object.assign({},e,{containerRef:t}))}));function sb(e,t){var n=Jn(e);return n?n.innerHeight:t?e.clientHeight:nr(e).height}function cb(e,t,n){e.closest&&!n&&e.closest(t);var r=e;do{if(pr(r,t))return r;r=r.parentElement}while(r&&r!==n&&r.nodeType===document.ELEMENT_NODE);return null}function fb(e,t){return Vo(arguments.length>2&&void 0!==arguments[2]?arguments[2]:document,e,t,{passive:!1})}function db(e,t){return!!function(e,t){var n=t.clientX,r=t.clientY;return cb(document.elementFromPoint(n,r),".rbc-event",e)}(e,t)}function pb(e,t){return!!function(e,t){var n=t.clientX,r=t.clientY;return cb(document.elementFromPoint(n,r),".rbc-show-more",e)}(e,t)}function vb(e){var t=e;return e.touches&&e.touches.length&&(t=e.touches[0]),{clientX:t.clientX,clientY:t.clientY,pageX:t.pageX,pageY:t.pageY}}ub.propTypes={popupOffset:be.oneOfType([be.number,be.shape({x:be.number,y:be.number})]),overlay:be.shape({position:be.object,events:be.array,date:be.instanceOf(Date),end:be.instanceOf(Date)}),accessors:be.object.isRequired,localizer:be.object.isRequired,components:be.object.isRequired,getters:be.object.isRequired,selected:be.object,handleSelectEvent:be.func,handleDoubleClickEvent:be.func,handleKeyPressEvent:be.func,handleDragStart:be.func,onHide:be.func,overlayDisplay:be.func};var hb=function(){return f((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.global,o=void 0!==r&&r,a=n.longPressThreshold,i=void 0===a?250:a,l=n.validContainers,u=void 0===l?[]:l;s(this,e),this._initialEvent=null,this.selecting=!1,this.isDetached=!1,this.container=t,this.globalMouse=!t||o,this.longPressThreshold=i,this.validContainers=u,this._listeners=Object.create(null),this._handleInitialEvent=this._handleInitialEvent.bind(this),this._handleMoveEvent=this._handleMoveEvent.bind(this),this._handleTerminatingEvent=this._handleTerminatingEvent.bind(this),this._keyListener=this._keyListener.bind(this),this._dropFromOutsideListener=this._dropFromOutsideListener.bind(this),this._dragOverFromOutsideListener=this._dragOverFromOutsideListener.bind(this),this._removeTouchMoveWindowListener=fb("touchmove",(function(){}),window),this._removeKeyDownListener=fb("keydown",this._keyListener),this._removeKeyUpListener=fb("keyup",this._keyListener),this._removeDropFromOutsideListener=fb("drop",this._dropFromOutsideListener),this._removeDragOverFromOutsideListener=fb("dragover",this._dragOverFromOutsideListener),this._addInitialEventListener()}),[{key:"on",value:function(e,t){var n=this._listeners[e]||(this._listeners[e]=[]);return n.push(t),{remove:function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}}}},{key:"emit",value:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o;return(this._listeners[e]||[]).forEach((function(e){void 0===o&&(o=e.apply(void 0,n))})),o}},{key:"teardown",value:function(){this._initialEvent=null,this._initialEventData=null,this._selectRect=null,this.selecting=!1,this._lastClickData=null,this.isDetached=!0,this._listeners=Object.create(null),this._removeTouchMoveWindowListener&&this._removeTouchMoveWindowListener(),this._removeInitialEventListener&&this._removeInitialEventListener(),this._removeEndListener&&this._removeEndListener(),this._onEscListener&&this._onEscListener(),this._removeMoveListener&&this._removeMoveListener(),this._removeKeyUpListener&&this._removeKeyUpListener(),this._removeKeyDownListener&&this._removeKeyDownListener(),this._removeDropFromOutsideListener&&this._removeDropFromOutsideListener(),this._removeDragOverFromOutsideListener&&this._removeDragOverFromOutsideListener()}},{key:"isSelected",value:function(e){var t=this._selectRect;return!(!t||!this.selecting)&&mb(t,gb(e))}},{key:"filter",value:function(e){return this._selectRect&&this.selecting?e.filter(this.isSelected,this):[]}},{key:"_addLongPressListener",value:function(e,t){var n=this,r=null,o=null,a=null,i=function(t){r=setTimeout((function(){u(),e(t)}),n.longPressThreshold),o=fb("touchmove",(function(){return u()})),a=fb("touchend",(function(){return u()}))},l=fb("touchstart",i),u=function(){r&&clearTimeout(r),o&&o(),a&&a(),r=null,o=null,a=null};return t&&i(t),function(){u(),l()}}},{key:"_addInitialEventListener",value:function(){var e=this,t=fb("mousedown",(function(t){e._removeInitialEventListener(),e._handleInitialEvent(t),e._removeInitialEventListener=fb("mousedown",e._handleInitialEvent)})),n=fb("touchstart",(function(t){e._removeInitialEventListener(),e._removeInitialEventListener=e._addLongPressListener(e._handleInitialEvent,t)}));this._removeInitialEventListener=function(){t(),n()}}},{key:"_dropFromOutsideListener",value:function(e){var t=vb(e),n=t.pageX,r=t.pageY,o=t.clientX,a=t.clientY;this.emit("dropFromOutside",{x:n,y:r,clientX:o,clientY:a}),e.preventDefault()}},{key:"_dragOverFromOutsideListener",value:function(e){var t=vb(e),n=t.pageX,r=t.pageY,o=t.clientX,a=t.clientY;this.emit("dragOverFromOutside",{x:n,y:r,clientX:o,clientY:a}),e.preventDefault()}},{key:"_handleInitialEvent",value:function(e){if(this._initialEvent=e,!this.isDetached){var t,r=vb(e),o=r.clientX,a=r.clientY,i=r.pageX,l=r.pageY,u=this.container();if(3!==e.which&&2!==e.button&&function(e,t,n){return!e||Xn(e,document.elementFromPoint(t,n))}(u,o,a)){if(!this.globalMouse&&u&&!Xn(u,e.target)){var s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;"object"!==n(e)&&(e={top:e,left:e,right:e,bottom:e});return e}(0),c=s.top,f=s.left,d=s.bottom,p=s.right;if(!mb({top:(t=gb(u)).top-c,left:t.left-f,bottom:t.bottom+d,right:t.right+p},{top:l,left:i}))return}if(!1!==this.emit("beforeSelect",this._initialEventData={isTouch:/^touch/.test(e.type),x:i,y:l,clientX:o,clientY:a}))switch(e.type){case"mousedown":this._removeEndListener=fb("mouseup",this._handleTerminatingEvent),this._onEscListener=fb("keydown",this._handleTerminatingEvent),this._removeMoveListener=fb("mousemove",this._handleMoveEvent);break;case"touchstart":this._handleMoveEvent(e),this._removeEndListener=fb("touchend",this._handleTerminatingEvent),this._removeMoveListener=fb("touchmove",this._handleMoveEvent)}}}}},{key:"_isWithinValidContainer",value:function(e){var t=e.target,n=this.validContainers;return!(n&&n.length&&t)||n.some((function(e){return!!t.closest(e)}))}},{key:"_handleTerminatingEvent",value:function(e){var t=this.selecting,n=this._selectRect;if(!t&&e.type.includes("key")&&(e=this._initialEvent),this.selecting=!1,this._removeEndListener&&this._removeEndListener(),this._removeMoveListener&&this._removeMoveListener(),this._selectRect=null,this._initialEvent=null,this._initialEventData=null,e){var r=!this.container||Xn(this.container(),e.target),o=this._isWithinValidContainer(e);return"Escape"!==e.key&&o?!t&&r?this._handleClickEvent(e):t?this.emit("select",n):this.emit("reset"):this.emit("reset")}}},{key:"_handleClickEvent",value:function(e){var t=vb(e),n=t.pageX,r=t.pageY,o=t.clientX,a=t.clientY,i=(new Date).getTime();return this._lastClickData&&i-this._lastClickData.timestamp<250?(this._lastClickData=null,this.emit("doubleClick",{x:n,y:r,clientX:o,clientY:a})):(this._lastClickData={timestamp:i},this.emit("click",{x:n,y:r,clientX:o,clientY:a}))}},{key:"_handleMoveEvent",value:function(e){if(null!==this._initialEventData&&!this.isDetached){var t=this._initialEventData,n=t.x,r=t.y,o=vb(e),a=o.pageX,i=o.pageY,l=Math.abs(n-a),u=Math.abs(r-i),s=Math.min(a,n),c=Math.min(i,r),f=this.selecting,d=this.isClick(a,i);(!d||f||l||u)&&(f||d||this.emit("selectStart",this._initialEventData),d||(this.selecting=!0,this._selectRect={top:c,left:s,x:a,y:i,right:s+l,bottom:c+u},this.emit("selecting",this._selectRect)),e.preventDefault())}}},{key:"_keyListener",value:function(e){this.ctrl=e.metaKey||e.ctrlKey}},{key:"isClick",value:function(e,t){var n=this._initialEventData,r=n.x,o=n.y;return!n.isTouch&&Math.abs(e-r)<=5&&Math.abs(t-o)<=5}}])}();function mb(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=gb(e),o=r.top,a=r.left,i=r.right,l=void 0===i?a:i,u=r.bottom,s=void 0===u?o:u,c=gb(t),f=c.top,d=c.left,p=c.right,v=void 0===p?d:p,h=c.bottom;return!(s-n<f||o+n>(void 0===h?f:h)||l-n<d||a+n>v)}function gb(e){if(!e.getBoundingClientRect)return e;var t=e.getBoundingClientRect(),n=t.left+yb("left"),r=t.top+yb("top");return{top:r,left:n,right:(e.offsetWidth||0)+n,bottom:(e.offsetHeight||0)+r}}function yb(e){return"left"===e?window.pageXOffset||document.body.scrollLeft||0:"top"===e?window.pageYOffset||document.body.scrollTop||0:void 0}var bb=function(e){function t(e,n){var r;return s(this,t),(r=h(this,t,[e,n])).state={selecting:!1},r.containerRef=O.createRef(),r}return g(t,e),f(t,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable()}},{key:"componentDidUpdate",value:function(e){!e.selectable&&this.props.selectable&&this._selectable(),e.selectable&&!this.props.selectable&&this._teardownSelectable()}},{key:"render",value:function(){var e=this.props,t=e.range,n=e.getNow,r=e.getters,o=e.date,a=e.components.dateCellWrapper,i=e.localizer,l=this.state,u=l.selecting,s=l.startIdx,c=l.endIdx,f=n();return ce.createElement("div",{className:"rbc-row-bg",ref:this.containerRef},t.map((function(e,n){var l=u&&n>=s&&n<=c,d=r.dayProp(e),p=d.className,v=d.style;return ce.createElement(a,{key:n,value:e,range:t},ce.createElement("div",{style:v,className:D("rbc-day-bg",p,l&&"rbc-selected-cell",i.isSameDate(e,f)&&"rbc-today",o&&i.neq(o,e,"month")&&"rbc-off-range-bg")}))})))}},{key:"_selectable",value:function(){var e=this,t=this.containerRef.current,n=this._selector=new hb(this.props.container,{longPressThreshold:this.props.longPressThreshold}),r=function(n,r){if(!db(t,n)&&!pb(t,n)){var o=gb(t),a=e.props,i=a.range,l=a.rtl;if(function(e,t){var n=t.x,r=t.y;return r>=e.top&&r<=e.bottom&&n>=e.left&&n<=e.right}(o,n)){var u=ob(o,n.x,l,i.length);e._selectSlot({startIdx:u,endIdx:u,action:r,box:n})}}e._initial={},e.setState({selecting:!1})};n.on("selecting",(function(r){var o=e.props,a=o.range,i=o.rtl,l=-1,u=-1;if(e.state.selecting||(Lt(e.props.onSelectStart,[r]),e._initial={x:r.x,y:r.y}),n.isSelected(t)){var s=gb(t),c=function(e,t,n,r,o){var a=-1,i=-1,l=r-1,u=rb(t,r),s=ob(t,n.x,o,r),c=t.top<n.y&&t.bottom>n.y,f=t.top<e.y&&t.bottom>e.y,d=e.y>t.bottom,p=t.top>e.y;return n.top<t.top&&n.bottom>t.bottom&&(a=0,i=l),c&&(p?(a=0,i=s):d&&(a=s,i=l)),f&&(a=i=o?l-Math.floor((e.x-t.left)/u):Math.floor((e.x-t.left)/u),c?s<a?a=s:i=s:e.y<n.y?i=l:a=0),{startIdx:a,endIdx:i}}(e._initial,s,r,a.length,i);l=c.startIdx,u=c.endIdx}e.setState({selecting:!0,startIdx:l,endIdx:u})})),n.on("beforeSelect",(function(t){if("ignoreEvents"===e.props.selectable)return!db(e.containerRef.current,t)})),n.on("click",(function(e){return r(e,"click")})),n.on("doubleClick",(function(e){return r(e,"doubleClick")})),n.on("select",(function(t){e._selectSlot(i(i({},e.state),{},{action:"select",bounds:t})),e._initial={},e.setState({selecting:!1}),Lt(e.props.onSelectEnd,[e.state])}))}},{key:"_teardownSelectable",value:function(){this._selector&&(this._selector.teardown(),this._selector=null)}},{key:"_selectSlot",value:function(e){var t=e.endIdx,n=e.startIdx,r=e.action,o=e.bounds,a=e.box;-1!==t&&-1!==n&&this.props.onSelectSlot&&this.props.onSelectSlot({start:n,end:t,action:r,bounds:o,box:a,resourceId:this.props.resourceId})}}])}(ce.Component),wb=(be.object.isRequired,be.object,be.bool,be.object.isRequired,be.object.isRequired,be.object.isRequired,be.object.isRequired,be.func,be.func,be.func,{segments:[],selected:{}}),kb=function(e,t){var n=e.selected;e.isAllDay;var r=e.accessors,o=e.getters,a=e.onSelect,i=e.onDoubleClick,l=e.onKeyPress,u=e.localizer,s=e.slotMetrics,c=e.components,f=e.resizable,d=s.continuesPrior(t),p=s.continuesAfter(t);return ce.createElement(yh,{event:t,getters:o,localizer:u,accessors:r,components:c,onSelect:a,onDoubleClick:i,onKeyPress:l,continuesPrior:d,continuesAfter:p,slotStart:s.first,slotEnd:s.last,selected:nb(t,n),resizable:f})},Eb=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:" ",o=Math.abs(t)/e*100+"%";return ce.createElement("div",{key:n,className:"rbc-row-segment",style:{WebkitFlexBasis:o,flexBasis:o,maxWidth:o}},r)},Sb=function(e){function t(){return s(this,t),h(this,t,arguments)}return g(t,e),f(t,[{key:"render",value:function(){var e=this,t=this.props,n=t.segments,r=t.slotMetrics.slots,o=t.className,a=1;return ce.createElement("div",{className:D(o,"rbc-row")},n.reduce((function(t,n,o){var i=n.event,l=n.left,u=n.right,s=n.span,c="_lvl_"+o,f=l-a,d=kb(e.props,i);return f&&t.push(Eb(r,f,"".concat(c,"_gap"))),t.push(Eb(r,s,c,d)),a=u+1,t}),[]))}}])}(ce.Component);Sb.defaultProps=i({},wb);var Db=function(e,t,n,r){for(var o=e.length,a=n+(r?1:-1);r?a--:++a<o;)if(t(e[a],a,e))return a;return-1},_b=Km,xb=Zy;var Ob=rn;var Mb=function(e){return e==e&&!Ob(e)},Cb=Mb,Tb=dy;var Pb=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}},Rb=function(e,t,n,r){var o=n.length,a=o,i=!r;if(null==e)return!a;for(e=Object(e);o--;){var l=n[o];if(i&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++o<a;){var u=(l=n[o])[0],s=e[u],c=l[1];if(i&&l[2]){if(void 0===s&&!(u in e))return!1}else{var f=new _b;if(r)var d=r(s,c,u,e,t,f);if(!(void 0===d?xb(c,s,3,r,f):d))return!1}}return!0},Nb=function(e){for(var t=Tb(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,Cb(o)]}return t},zb=Pb;var Lb=function(e){var t=Nb(e);return 1==t.length&&t[0][2]?zb(t[0][0],t[0][1]):function(n){return n===e||Rb(n,e,t)}},Ab=pg,jb=_n,Fb=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ib=/^\w*$/;var Ub=function(e,t){if(Ab(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!jb(e))||(Ib.test(e)||!Fb.test(e)||null!=t&&e in Object(t))},Wb=jm;function Hb(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],a=n.cache;if(a.has(o))return a.get(o);var i=e.apply(this,r);return n.cache=a.set(o,i)||a,i};return n.cache=new(Hb.Cache||Wb),n}Hb.Cache=Wb;var Vb=Hb;var Bb=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,$b=/\\(\\)?/g,Yb=function(e){var t=Vb(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Bb,(function(e,n,r,o){t.push(r?o.replace($b,"$1"):n||e)})),t}));var qb=function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o},Kb=qb,Qb=pg,Gb=_n,Xb=$t?$t.prototype:void 0,Jb=Xb?Xb.toString:void 0;var Zb=function e(t){if("string"==typeof t)return t;if(Qb(t))return Kb(t,e)+"";if(Gb(t))return Jb?Jb.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n},ew=Zb;var tw=pg,nw=Ub,rw=Yb,ow=function(e){return null==e?"":ew(e)};var aw=function(e,t){return tw(e)?e:nw(e,t)?[e]:rw(ow(e))},iw=_n;var lw=function(e){if("string"==typeof e||iw(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t},uw=aw,sw=lw;var cw=function(e,t){for(var n=0,r=(t=uw(t,e)).length;null!=e&&n<r;)e=e[sw(t[n++])];return n&&n==r?e:void 0},fw=cw;var dw=aw,pw=Rg,vw=pg,hw=pn,mw=un,gw=lw;var yw=function(e,t){return null!=e&&t in Object(e)},bw=function(e,t,n){for(var r=-1,o=(t=dw(t,e)).length,a=!1;++r<o;){var i=gw(t[r]);if(!(a=null!=e&&n(e,i)))break;e=e[i]}return a||++r!=o?a:!!(o=null==e?0:e.length)&&mw(o)&&hw(i,o)&&(vw(e)||pw(e))};var ww=Zy,kw=function(e,t,n){var r=null==e?void 0:fw(e,t);return void 0===r?n:r},Ew=function(e,t){return null!=e&&bw(e,t,yw)},Sw=Ub,Dw=Mb,_w=Pb,xw=lw;var Ow=function(e){return e};var Mw=cw;var Cw=function(e){return function(t){return null==t?void 0:t[e]}},Tw=function(e){return function(t){return Mw(t,e)}},Pw=Ub,Rw=lw;var Nw=Lb,zw=function(e,t){return Sw(e)&&Dw(t)?_w(xw(e),t):function(n){var r=kw(n,e);return void 0===r&&r===t?Ew(n,e):ww(t,r,3)}},Lw=Ow,Aw=pg,jw=function(e){return Pw(e)?Cw(Rw(e)):Tw(e)};var Fw=function(e){return"function"==typeof e?e:null==e?Lw:"object"==typeof e?Aw(e)?zw(e[0],e[1]):Nw(e):jw(e)},Iw=Db,Uw=Fw,Ww=jn,Hw=Math.max;var Vw=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:Ww(n);return o<0&&(o=Hw(r+o,0)),Iw(e,Uw(t),o)};function Bw(e){var t=e.dateRange,n=e.unit,r=void 0===n?"day":n,o=e.localizer;return{first:t[0],last:o.add(t[t.length-1],1,r)}}function $w(e){var t,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0,a=[],i=[];for(t=0;t<e.length;t++){for(r=e[t],n=0;n<a.length&&qw(r,a[n]);n++);n>=o?i.push(r):(a[n]||(a[n]=[])).push(r)}for(t=0;t<a.length;t++)a[t].sort((function(e,t){return e.left-t.left}));return{levels:a,extra:i}}function Yw(e,t,n,r,o){var a={start:r.start(e),end:r.end(e)},i={start:t,end:n};return o.inEventRange({event:a,range:i})}function qw(e,t){return t.some((function(t){return t.left<=e.right&&t.right>=e.left}))}function Kw(e,t,n,r){var o={start:n.start(e),end:n.end(e),allDay:n.allDay(e)},a={start:n.start(t),end:n.end(t),allDay:n.allDay(t)};return r.sortEvents({evtA:o,evtB:a})}var Qw=Math.ceil,Gw=Math.max;var Xw=function(e,t,n,r){for(var o=-1,a=Gw(Qw((t-e)/(n||1)),0),i=Array(a);a--;)i[r?a:++o]=e,e+=n;return i},Jw=Xw,Zw=yn,ek=Ln;var tk=function(e){return function(t,n,r){return r&&"number"!=typeof r&&Zw(t,n,r)&&(n=r=void 0),t=ek(t),void 0===n?(n=t,t=0):n=ek(n),r=void 0===r?t<n?1:-1:ek(r),Jw(t,n,r,e)}},nk=tk(),rk=function(e,t){return e.left<=t&&e.right>=t},ok=function(e,t){return e.filter((function(e){return rk(e,t)})).map((function(e){return e.event}))},ak=function(e){function t(){return s(this,t),h(this,t,arguments)}return g(t,e),f(t,[{key:"render",value:function(){for(var e=this.props,t=e.segments,n=e.slotMetrics.slots,r=$w(t).levels[0],o=1,a=1,i=[];o<=n;){var l="_lvl_"+o,u=r.filter((function(e){return rk(e,o)}))[0]||{},s=u.event,c=u.left,f=u.right,d=u.span;if(s){var p=Math.max(0,c-a);if(this.canRenderSlotEvent(c,d)){var v=kb(this.props,s);p&&i.push(Eb(n,p,l+"_gap")),i.push(Eb(n,d,l,v)),a=o=f+1}else p&&i.push(Eb(n,p,l+"_gap")),i.push(Eb(n,1,l,this.renderShowMore(t,o))),a=o+=1}else o++}return ce.createElement("div",{className:"rbc-row"},i)}},{key:"canRenderSlotEvent",value:function(e,t){var n=this.props.segments;return nk(e,e+t).every((function(e){return 1===ok(n,e).length}))}},{key:"renderShowMore",value:function(e,t){var n=this,r=this.props,o=r.localizer,a=r.slotMetrics,i=r.components,l=a.getEventsForSlot(t),u=ok(e,t),s=u.length;if(null!=i&&i.showMore){var c=i.showMore,f=a.getDateForSlot(t-1);return!!s&&ce.createElement(c,{localizer:o,slotDate:f,slot:t,count:s,events:l,remainingEvents:u})}return!!s&&ce.createElement("button",{type:"button",key:"sm_"+t,className:D("rbc-button-link","rbc-show-more"),onClick:function(e){return n.showMore(t,e)}},o.messages.showMore(s,u,l))}},{key:"showMore",value:function(e,t){t.preventDefault(),t.stopPropagation(),this.props.onShowMore(e,t.target)}}])}(ce.Component);ak.defaultProps=i({},wb);var ik=function(e){var t=e.children;return ce.createElement("div",{className:"rbc-row-content-scroll-container"},t)},lk=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function uk(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(r=e[n],o=t[n],!(r===o||lk(r)&&lk(o)))return!1;var r,o;return!0}function sk(e,t){void 0===t&&(t=uk);var n=null;function r(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var a=e.apply(this,r);return n={lastResult:a,lastArgs:r,lastThis:this},a}return r.clear=function(){n=null},r}var ck=function(e,t){return e[0].range===t[0].range&&e[0].events===t[0].events};function fk(){return sk((function(e){for(var t=e.range,n=e.events,r=e.maxRows,o=e.minRows,a=e.accessors,l=e.localizer,u=Bw({dateRange:t,localizer:l}),s=u.first,c=u.last,f=n.map((function(e){return function(e,t,n,r){var o=Bw({dateRange:t,localizer:r}),a=o.first,i=o.last,l=r.diff(a,i,"day"),u=r.max(r.startOf(n.start(e),"day"),a),s=r.min(r.ceil(n.end(e),"day"),i),c=Vw(t,(function(e){return r.isSameDate(e,u)})),f=r.diff(u,s,"day");return f=Math.min(f,l),{event:e,span:f=Math.max(f-r.segmentOffset,1),left:c+1,right:Math.max(c+f,1)}}(e,t,a,l)})),d=$w(f,Math.max(r-1,1)),p=d.levels,v=d.extra,h=v.length>0?o-1:o;p.length<h;)p.push([]);return{first:s,last:c,levels:p,extra:v,range:t,slots:t.length,clone:function(t){return fk()(i(i({},e),t))},getDateForSlot:function(e){return t[e]},getSlotForDate:function(e){return t.find((function(t){return l.isSameDate(t,e)}))},getEventsForSlot:function(e){return f.filter((function(t){return function(e,t){return e.left<=t&&e.right>=t}(t,e)})).map((function(e){return e.event}))},continuesPrior:function(e){return l.continuesPrior(a.start(e),s)},continuesAfter:function(e){var t=a.start(e),n=a.end(e);return l.continuesAfter(t,n,c)}}}),ck)}var dk=function(e){function n(){var e;s(this,n);for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return(e=h(this,n,[].concat(r))).handleSelectSlot=function(t){var n=e.props,r=n.range;(0,n.onSelectSlot)(r.slice(t.start,t.end+1),t)},e.handleShowMore=function(t,n){var r,o,a,i=e.props,l=i.range,u=i.onShowMore,s=e.slotMetrics(e.props),c=(r=e.containerRef.current,o=".rbc-row-bg",vr(r.querySelectorAll(o)))[0];c&&(a=c.children[t-1]),u(s.getEventsForSlot(t),l[t-1],a,t,n)},e.getContainer=function(){var t=e.props.container;return t?t():e.containerRef.current},e.renderHeadingCell=function(t,n){var r=e.props,o=r.renderHeader,a=r.getNow,i=r.localizer;return o({date:t,key:"header_".concat(n),className:D("rbc-date-cell",i.isSameDate(t,a())&&"rbc-now")})},e.renderDummy=function(){var t=e.props,n=t.className,r=t.range,o=t.renderHeader,a=t.showAllEvents;return ce.createElement("div",{className:n,ref:e.containerRef},ce.createElement("div",{className:D("rbc-row-content",a&&"rbc-row-content-scrollable")},o&&ce.createElement("div",{className:"rbc-row",ref:e.headingRowRef},r.map(e.renderHeadingCell)),ce.createElement("div",{className:"rbc-row",ref:e.eventRowRef},ce.createElement("div",{className:"rbc-row-segment"},ce.createElement("div",{className:"rbc-event"},ce.createElement("div",{className:"rbc-event-content"}," "))))))},e.containerRef=O.createRef(),e.headingRowRef=O.createRef(),e.eventRowRef=O.createRef(),e.slotMetrics=fk(),e}return g(n,e),f(n,[{key:"getRowLimit",value:function(){var e,t=sb(this.eventRowRef.current),n=null!==(e=this.headingRowRef)&&void 0!==e&&e.current?sb(this.headingRowRef.current):0,r=sb(this.containerRef.current)-n;return Math.max(Math.floor(r/t),1)}},{key:"render",value:function(){var e=this.props,n=e.date,r=e.rtl,o=e.range,a=e.className,i=e.selected,l=e.selectable,u=e.renderForMeasure,s=e.accessors,c=e.getters,f=e.components,d=e.getNow,p=e.renderHeader,v=e.onSelect,h=e.localizer,m=e.onSelectStart,g=e.onSelectEnd,y=e.onDoubleClick,b=e.onKeyPress,w=e.resourceId,k=e.longPressThreshold,E=e.isAllDay,S=e.resizable,_=e.showAllEvents;if(u)return this.renderDummy();var x=this.slotMetrics(this.props),O=x.levels,M=x.extra,C=_?ik:t,T=f.weekWrapper,P={selected:i,accessors:s,getters:c,localizer:h,components:f,onSelect:v,onDoubleClick:y,onKeyPress:b,resourceId:w,slotMetrics:x,resizable:S};return ce.createElement("div",{className:a,role:"rowgroup",ref:this.containerRef},ce.createElement(bb,{localizer:h,date:n,getNow:d,rtl:r,range:o,selectable:l,container:this.getContainer,getters:c,onSelectStart:m,onSelectEnd:g,onSelectSlot:this.handleSelectSlot,components:f,longPressThreshold:k,resourceId:w}),ce.createElement("div",{className:D("rbc-row-content",_&&"rbc-row-content-scrollable"),role:"row"},p&&ce.createElement("div",{className:"rbc-row ",ref:this.headingRowRef},o.map(this.renderHeadingCell)),ce.createElement(C,null,ce.createElement(T,Object.assign({isAllDay:E},P,{rtl:this.props.rtl}),O.map((function(e,t){return ce.createElement(Sb,Object.assign({key:t,segments:e},P))})),!!M.length&&ce.createElement(ak,Object.assign({segments:M,onShowMore:this.handleShowMore},P))))))}}])}(ce.Component);dk.defaultProps={minRows:0,maxRows:1/0};var pk=function(e){var t=e.label;return ce.createElement("span",{role:"columnheader","aria-sort":"none"},t)},vk=function(e){var t=e.label,n=e.drilldownView,r=e.onDrillDown;return n?ce.createElement("button",{type:"button",className:"rbc-button-link",onClick:r},t):ce.createElement("span",null,t)},hk=["date","className"],mk=function(e){function t(){var e;s(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=h(this,t,[].concat(r))).getContainer=function(){return e.containerRef.current},e.renderWeek=function(t,n){var r=e.props,o=r.events,a=r.components,i=r.selectable,l=r.getNow,u=r.selected,s=r.date,c=r.localizer,f=r.longPressThreshold,d=r.accessors,p=r.getters,v=r.showAllEvents,h=e.state,m=h.needLimitMeasure,g=h.rowLimit,y=function(e,t,n,r,o){return e.filter((function(e){return Yw(e,t,n,r,o)}))}(Ft(o),t[0],t[t.length-1],d,c),b=function(e,t,n){var r=Ft(e),o=[],a=[];r.forEach((function(e){var r=t.start(e),i=t.end(e);n.daySpan(r,i)>1?o.push(e):a.push(e)}));var i=o.sort((function(e,r){return Kw(e,r,t,n)})),l=a.sort((function(e,r){return Kw(e,r,t,n)}));return[].concat(Ft(i),Ft(l))}(y,d,c);return ce.createElement(dk,{key:n,ref:0===n?e.slotRowRef:void 0,container:e.getContainer,className:"rbc-month-row",getNow:l,date:s,range:t,events:b,maxRows:v?1/0:g,selected:u,selectable:i,components:a,accessors:d,getters:p,localizer:c,renderHeader:e.readerDateHeading,renderForMeasure:m,onShowMore:e.handleShowMore,onSelect:e.handleSelectEvent,onDoubleClick:e.handleDoubleClickEvent,onKeyPress:e.handleKeyPressEvent,onSelectSlot:e.handleSelectSlot,longPressThreshold:f,rtl:e.props.rtl,resizable:e.props.resizable,showAllEvents:v})},e.readerDateHeading=function(t){var n=t.date,r=t.className,o=u(t,hk),a=e.props,i=a.date,l=a.getDrilldownView,s=a.localizer,c=s.neq(n,i,"month"),f=s.isSameDate(n,i),d=l(n),p=s.format(n,"dateFormat"),v=e.props.components.dateHeader||vk;return ce.createElement("div",Object.assign({},o,{className:D(r,c&&"rbc-off-range",f&&"rbc-current"),role:"cell"}),ce.createElement(v,{label:p,date:n,drilldownView:d,isOffRange:c,onDrillDown:function(t){return e.handleHeadingClick(n,d,t)}}))},e.handleSelectSlot=function(t,n){e._pendingSelection=e._pendingSelection.concat(t),clearTimeout(e._selectTimer),e._selectTimer=setTimeout((function(){return e.selectDates(n)}))},e.handleHeadingClick=function(t,n,r){r.preventDefault(),e.clearSelection(),Lt(e.props.onDrillDown,[t,n])},e.handleSelectEvent=function(){e.clearSelection();for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onSelectEvent,n)},e.handleDoubleClickEvent=function(){e.clearSelection();for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onDoubleClickEvent,n)},e.handleKeyPressEvent=function(){e.clearSelection();for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onKeyPressEvent,n)},e.handleShowMore=function(t,n,r,o,a){var i=e.props,l=i.popup,u=i.onDrillDown,s=i.onShowMore,c=i.getDrilldownView,f=i.doShowMoreDrillDown;if(e.clearSelection(),l){var d=or(r,e.containerRef.current);e.setState({overlay:{date:n,events:t,position:d,target:a}})}else f&&Lt(u,[n,c(n)||De.DAY]);Lt(s,[t,n,o])},e.overlayDisplay=function(){e.setState({overlay:null})},e.state={rowLimit:5,needLimitMeasure:!0,date:null},e.containerRef=O.createRef(),e.slotRowRef=O.createRef(),e._bgRows=[],e._pendingSelection=[],e}return g(t,e),f(t,[{key:"componentDidMount",value:function(){var e,t=this;this.state.needLimitMeasure&&this.measureRowLimit(this.props),window.addEventListener("resize",this._resizeListener=function(){e||dr((function(){e=!1,t.setState({needLimitMeasure:!0})}))},!1)}},{key:"componentDidUpdate",value:function(){this.state.needLimitMeasure&&this.measureRowLimit(this.props)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this._resizeListener,!1)}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.localizer,r=e.className,o=n.visibleDays(t,n),a=Vn(o,7);return this._weekCount=a.length,ce.createElement("div",{className:D("rbc-month-view",r),role:"table","aria-label":"Month View",ref:this.containerRef},ce.createElement("div",{className:"rbc-row rbc-month-header",role:"row"},this.renderHeaders(a[0])),a.map(this.renderWeek),this.props.popup&&this.renderOverlay())}},{key:"renderHeaders",value:function(e){var t=this.props,n=t.localizer,r=t.components,o=e[0],a=e[e.length-1],i=r.header||pk;return n.range(o,a,"day").map((function(e,t){return ce.createElement("div",{key:"header_"+t,className:"rbc-header"},ce.createElement(i,{date:e,localizer:n,label:n.format(e,"weekdayFormat")}))}))}},{key:"renderOverlay",value:function(){var e,t,n=this,r=null!==(e=null===(t=this.state)||void 0===t?void 0:t.overlay)&&void 0!==e?e:{},o=this.props,a=o.accessors,i=o.localizer,l=o.components,u=o.getters,s=o.selected,c=o.popupOffset,f=o.handleDragStart;return ce.createElement(ub,{overlay:r,accessors:a,localizer:i,components:l,getters:u,selected:s,popupOffset:c,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:f,show:!!r.position,overlayDisplay:this.overlayDisplay,onHide:function(){return n.setState({overlay:null})}})}},{key:"measureRowLimit",value:function(){this.setState({needLimitMeasure:!1,rowLimit:this.slotRowRef.current.getRowLimit()})}},{key:"selectDates",value:function(e){var t=this._pendingSelection.slice();this._pendingSelection=[],t.sort((function(e,t){return+e-+t}));var n=new Date(t[0]),r=new Date(t[t.length-1]);r.setDate(t[t.length-1].getDate()+1),Lt(this.props.onSelectSlot,{slots:t,start:n,end:r,action:e.action,bounds:e.bounds,box:e.box})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.date;return{date:n,needLimitMeasure:e.localizer.neq(n,t.date,"month")}}}])}(ce.Component);function gk(e,t){var n=Jn(e);return n?n.innerWidth:t?e.clientWidth:nr(e).width}mk.range=function(e,t){var n=t.localizer;return{start:n.firstVisibleDay(e,n),end:n.lastVisibleDay(e,n)}},mk.navigate=function(e,t,n){var r=n.localizer;switch(t){case Se.PREVIOUS:return r.add(e,-1,"month");case Se.NEXT:return r.add(e,1,"month");default:return e}},mk.title=function(e,t){return t.localizer.format(e,"monthHeaderFormat")};var yk=function(e){var t=e.min,n=e.max,r=e.step,o=e.slots,a=e.localizer;return"".concat(+a.startOf(t,"minutes"))+"".concat(+a.startOf(n,"minutes"))+"".concat(r,"-").concat(o)};function bk(e){for(var t=e.min,n=e.max,r=e.step,o=e.timeslots,a=e.localizer,i=yk({start:t,end:n,step:r,timeslots:o,localizer:a}),l=1+a.getTotalMin(t,n),u=a.getMinutesFromMidnight(t),s=Math.ceil((l-1)/(r*o)),c=s*o,f=new Array(s),d=new Array(c),p=0;p<s;p++){f[p]=new Array(o);for(var v=0;v<o;v++){var h=p*o+v,m=h*r;d[h]=f[p][v]=a.getSlotDate(t,u,m)}}var g=d.length*r;function y(e){var n=a.diff(t,e,"minutes")+a.getDstOffset(t,e);return Math.min(n,l)}return d.push(a.getSlotDate(t,u,g)),{groups:f,update:function(e){return yk(e)!==i?bk(e):this},dateIsInGroup:function(e,t){var r=f[t+1];return a.inRange(e,f[t][0],r?r[0]:n,"minutes")},nextSlot:function(e){var t=d[Math.min(d.findIndex((function(t){return t===e||a.eq(t,e)}))+1,d.length-1)];return a.eq(t,e)&&(t=a.add(e,r,"minutes")),t},closestSlotToPosition:function(e){var t=Math.min(d.length-1,Math.max(0,Math.floor(e*c)));return d[t]},closestSlotFromPoint:function(e,t){var n=Math.abs(t.top-t.bottom);return this.closestSlotToPosition((e.y-t.top)/n)},closestSlotFromDate:function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(a.lt(e,t,"minutes"))return d[0];if(a.gt(e,n,"minutes"))return d[d.length-1];var i=a.diff(t,e,"minutes");return d[(i-i%r)/r+o]},startsBeforeDay:function(e){return a.lt(e,t,"day")},startsAfterDay:function(e){return a.gt(e,n,"day")},startsBefore:function(e){return a.lt(a.merge(t,e),t,"minutes")},startsAfter:function(e){return a.gt(a.merge(n,e),n,"minutes")},getRange:function(e,o,i,l){i||(e=a.min(n,a.max(t,e))),l||(o=a.min(n,a.max(t,o)));var u=y(e),s=y(o),f=s>r*c&&!a.eq(n,o)?(u-r)/(r*c)*100:u/(r*c)*100;return{top:f,height:s/(r*c)*100-f,start:y(e),startDate:e,end:y(o),endDate:o}},getCurrentTimePosition:function(e){return y(e)/(r*c)*100}}}var wk=Rg,kk=pg,Ek=$t?$t.isConcatSpreadable:void 0;var Sk=dg,Dk=function(e){return kk(e)||wk(e)||!!(Ek&&e&&e[Ek])};var _k=function e(t,n,r,o,a){var i=-1,l=t.length;for(r||(r=Dk),a||(a=[]);++i<l;){var u=t[i];n>0&&r(u)?n>1?e(u,n-1,r,o,a):Sk(a,u):o||(a[a.length]=u)}return a};var xk=function(e){return function(t,n,r){for(var o=-1,a=Object(t),i=r(t),l=i.length;l--;){var u=i[e?l:++o];if(!1===n(a[u],u,a))break}return t}}(),Ok=dy;var Mk=function(e,t){return e&&xk(e,t,Ok)},Ck=fn;var Tk=function(e,t){return function(n,r){if(null==n)return n;if(!Ck(n))return e(n,r);for(var o=n.length,a=t?o:-1,i=Object(n);(t?a--:++a<o)&&!1!==r(i[a],a,i););return n}}(Mk),Pk=fn;var Rk=_n;var Nk=function(e,t){if(e!==t){var n=void 0!==e,r=null===e,o=e==e,a=Rk(e),i=void 0!==t,l=null===t,u=t==t,s=Rk(t);if(!l&&!s&&!a&&e>t||a&&i&&u&&!l&&!s||r&&i&&u||!n&&u||!o)return 1;if(!r&&!a&&!s&&e<t||s&&n&&o&&!r&&!a||l&&n&&o||!i&&o||!u)return-1}return 0};var zk=qb,Lk=cw,Ak=Fw,jk=function(e,t){var n=-1,r=Pk(e)?Array(e.length):[];return Tk(e,(function(e,o,a){r[++n]=t(e,o,a)})),r},Fk=function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e},Ik=Wg,Uk=function(e,t,n){for(var r=-1,o=e.criteria,a=t.criteria,i=o.length,l=n.length;++r<i;){var u=Nk(o[r],a[r]);if(u)return r>=l?u:u*("desc"==n[r]?-1:1)}return e.index-t.index},Wk=Ow,Hk=pg;var Vk=function(e,t,n){t=t.length?zk(t,(function(e){return Hk(e)?function(t){return Lk(t,1===e.length?e[0]:e)}:e})):[Wk];var r=-1;t=zk(t,Ik(Ak));var o=jk(e,(function(e,n,o){return{criteria:zk(t,(function(t){return t(e)})),index:++r,value:e}}));return Fk(o,(function(e,t){return Uk(e,t,n)}))};var Bk=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)},$k=Math.max;var Yk=function(e,t,n){return t=$k(void 0===t?e.length-1:t,0),function(){for(var r=arguments,o=-1,a=$k(r.length-t,0),i=Array(a);++o<a;)i[o]=r[t+o];o=-1;for(var l=Array(t+1);++o<t;)l[o]=r[o];return l[t]=n(i),Bk(e,this,l)}};var qk=function(e){return function(){return e}},Kk=rm,Qk=function(){try{var e=Kk(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),Gk=qk,Xk=Qk,Jk=Xk?function(e,t){return Xk(e,"toString",{configurable:!0,enumerable:!1,value:Gk(t),writable:!0})}:Ow,Zk=Date.now;var eE=function(e){var t=0,n=0;return function(){var r=Zk(),o=16-(r-n);if(n=r,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}},tE=eE(Jk),nE=Ow,rE=Yk,oE=tE;var aE=function(e,t){return oE(rE(e,t,nE),e+"")},iE=_k,lE=Vk,uE=yn,sE=aE((function(e,t){if(null==e)return[];var n=t.length;return n>1&&uE(e,t[0],t[1])?t=[]:n>2&&uE(t[0],t[1],t[2])&&(t=[t[0]]),lE(e,iE(t,1),[])})),cE=function(){return f((function e(t,n){var r=n.accessors,o=n.slotMetrics;s(this,e);var a=o.getRange(r.start(t),r.end(t)),i=a.start,l=a.startDate,u=a.end,c=a.endDate,f=a.top,d=a.height;this.start=i,this.end=u,this.startMs=+l,this.endMs=+c,this.top=f,this.height=d,this.data=t}),[{key:"_width",get:function(){if(this.rows){var e=this.rows.reduce((function(e,t){return Math.max(e,t.leaves.length+1)}),0)+1;return 100/e}return this.leaves?(100-this.container._width)/(this.leaves.length+1):this.row._width}},{key:"width",get:function(){var e=this._width,t=Math.min(100,1.7*this._width);if(this.rows)return t;if(this.leaves)return this.leaves.length>0?t:e;var n=this.row.leaves;return n.indexOf(this)===n.length-1?e:t}},{key:"xOffset",get:function(){if(this.rows)return 0;if(this.leaves)return this.container._width;var e=this.row,t=e.leaves,n=e.xOffset,r=e._width;return n+(t.indexOf(this)+1)*r}}])}();function fE(e,t,n){return Math.abs(t.start-e.start)<n||t.start>e.start&&t.start<e.end}function dE(e){for(var t=e.events,n=e.minimumStartDifference,r=e.slotMetrics,o=e.accessors,a=function(e){for(var t=sE(e,["startMs",function(e){return-e.endMs}]),n=[];t.length>0;){var r=t.shift();n.push(r);for(var o=0;o<t.length;o++){var a=t[o];if(!(r.endMs>a.startMs)){if(o>0){var i=t.splice(o,1)[0];n.push(i)}break}}}return n}(t.map((function(e){return new cE(e,{slotMetrics:r,accessors:o})}))),i=[],l=function(){var e=a[u],t=i.find((function(t){return t.end>e.start||Math.abs(e.start-t.start)<n}));if(!t)return e.rows=[],i.push(e),1;e.container=t;for(var r=null,o=t.rows.length-1;!r&&o>=0;o--)fE(t.rows[o],e,n)&&(r=t.rows[o]);r?(r.leaves.push(e),e.row=r):(e.leaves=[],t.rows.push(e))},u=0;u<a.length;u++)l();return a.map((function(e){return{event:e.data,style:{top:e.top,height:e.height,width:e.width,xOffset:Math.max(0,e.xOffset)}}}))}function pE(e,t,n){for(var r=0;r<e.friends.length;++r)if(!(n.indexOf(e.friends[r])>-1)){t=t>e.friends[r].idx?t:e.friends[r].idx,n.push(e.friends[r]);var o=pE(e.friends[r],t,n);t=t>o?t:o}return t}var vE={overlap:dE,"no-overlap":function(e){var t=dE({events:e.events,minimumStartDifference:e.minimumStartDifference,slotMetrics:e.slotMetrics,accessors:e.accessors});t.sort((function(e,t){return e=e.style,t=t.style,e.top!==t.top?e.top>t.top?1:-1:e.height!==t.height?e.top+e.height<t.top+t.height?1:-1:0}));for(var n=0;n<t.length;++n)t[n].friends=[],delete t[n].style.left,delete t[n].style.left,delete t[n].idx,delete t[n].size;for(var r=0;r<t.length-1;++r)for(var o=t[r],a=o.style.top,i=o.style.top+o.style.height,l=r+1;l<t.length;++l){var u=t[l],s=u.style.top,c=u.style.top+u.style.height;(s>=a&&c<=i||c>a&&c<=i||s>=a&&s<i)&&(o.friends.push(u),u.friends.push(o))}for(var f=0;f<t.length;++f){for(var d=t[f],p=[],v=0;v<100;++v)p.push(1);for(var h=0;h<d.friends.length;++h)void 0!==d.friends[h].idx&&(p[d.friends[h].idx]=0);d.idx=p.indexOf(1)}for(var m=0;m<t.length;++m){var g;if(!t[m].size){var y=[];g=100/(pE(t[m],0,y)+1),t[m].size=g;for(var b=0;b<y.length;++b)y[b].size=g}}for(var w=0;w<t.length;++w){var k=t[w];k.style.left=k.idx*k.size;for(var E=0,S=0;S<k.friends.length;++S){var D=k.friends[S].idx;E=E>D?E:D}E<=k.idx&&(k.size=100-k.idx*k.size);var _=0===k.idx?0:3;k.style.width="calc(".concat(k.size,"% - ").concat(_,"px)"),k.style.height="calc(".concat(k.style.height,"% - 2px)"),k.style.xOffset="calc(".concat(k.style.left,"% + ").concat(_,"px)")}return t}};var hE=function(e){function n(){return s(this,n),h(this,n,arguments)}return g(n,e),f(n,[{key:"render",value:function(){var e=this.props,n=e.renderSlot,r=e.resource,o=e.group,a=e.getters,i=e.components,l=(void 0===i?{}:i).timeSlotWrapper,u=void 0===l?t:l,s=a?a.slotGroupProp(o):{};return ce.createElement("div",Object.assign({className:"rbc-timeslot-group"},s),o.map((function(e,t){var o=a?a.slotProp(e,r):{};return ce.createElement(u,{key:t,value:e,resource:r},ce.createElement("div",Object.assign({},o,{className:D("rbc-time-slot",o.className)}),n&&n(e,t)))})))}}])}(O.Component);function mE(e){return"string"==typeof e?e:e+"%"}function gE(e){var t=e.style,n=e.className,r=e.event,a=e.accessors,l=e.rtl,u=e.selected,s=e.label,c=e.continuesPrior,f=e.continuesAfter,d=e.getters,p=e.onClick,v=e.onDoubleClick,h=e.isBackgroundEvent,m=e.onKeyPress,g=e.components,y=g.event,b=g.eventWrapper,w=a.title(r),k=a.tooltip(r),E=a.end(r),S=a.start(r),_=d.eventProp(r,S,E,u),x=[ce.createElement("div",{key:"1",className:"rbc-event-label"},s),ce.createElement("div",{key:"2",className:"rbc-event-content"},y?ce.createElement(y,{event:r,title:w}):w)],O=t.height,M=t.top,C=t.width,T=t.xOffset,P=i(i({},_.style),{},o({top:mE(M),height:mE(O),width:mE(C)},l?"right":"left",mE(T)));return ce.createElement(b,Object.assign({type:"time"},e),ce.createElement("div",{role:"button",tabIndex:0,onClick:p,onDoubleClick:v,style:P,onKeyDown:m,title:k?("string"==typeof s?s+": ":"")+k:void 0,className:D(h?"rbc-background-event":"rbc-event",n,_.className,{"rbc-selected":u,"rbc-event-continues-earlier":c,"rbc-event-continues-later":f})},x))}var yE=function(e){var t=e.children,n=e.className,r=e.style,o=e.innerRef;return ce.createElement("div",{className:n,style:r,ref:o},t)},bE=ce.forwardRef((function(e,t){return ce.createElement(yE,Object.assign({},e,{innerRef:t}))})),wE=["dayProp"],kE=["eventContainerWrapper"],EE=function(e){function t(){var e;s(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=h(this,t,[].concat(r))).state={selecting:!1,timeIndicatorPosition:null},e.intervalTriggered=!1,e.renderEvents=function(t){var n=t.events,r=t.isBackgroundEvent,o=e.props,a=o.rtl,l=o.selected,u=o.accessors,s=o.localizer,c=o.getters,f=o.components,d=o.step,p=o.timeslots,v=o.dayLayoutAlgorithm,h=o.resizable,m=e.slotMetrics,g=s.messages,y=function(e){e.events,e.minimumStartDifference,e.slotMetrics,e.accessors;var t=e.dayLayoutAlgorithm,n=t;return t in vE&&(n=vE[t]),function(e){return!!(e&&e.constructor&&e.call&&e.apply)}(n)?n.apply(this,arguments):[]}({events:n,accessors:u,slotMetrics:m,minimumStartDifference:Math.ceil(d*p/2),dayLayoutAlgorithm:v});return y.map((function(t,n){var o,d,p=t.event,v=t.style,y=u.end(p),b=u.start(p),w=null!==(o=u.eventId(p))&&void 0!==o?o:"evt_"+n,k="eventTimeRangeFormat",E=m.startsBeforeDay(b),S=m.startsAfterDay(y);E?k="eventTimeRangeEndFormat":S&&(k="eventTimeRangeStartFormat"),d=E&&S?g.allDay:s.format({start:b,end:y},k);var D=E||m.startsBefore(b),_=S||m.startsAfter(y);return ce.createElement(gE,{style:v,event:p,label:d,key:w,getters:c,rtl:a,components:f,continuesPrior:D,continuesAfter:_,accessors:u,resource:e.props.resource,selected:nb(p,l),onClick:function(t){return e._select(i(i(i({},p),e.props.resource&&{sourceResource:e.props.resource}),r&&{isBackgroundEvent:!0}),t)},onDoubleClick:function(t){return e._doubleClick(p,t)},isBackgroundEvent:r,onKeyPress:function(t){return e._keyPress(p,t)},resizable:h})}))},e._selectable=function(){var t=e.containerRef.current,n=e.props,r=n.longPressThreshold,o=n.localizer,a=e._selector=new hb((function(){return t}),{longPressThreshold:r}),l=function(t){var n=e.props.onSelecting,r=e.state||{},a=u(t),i=a.startDate,l=a.endDate;n&&(o.eq(r.startDate,i,"minutes")&&o.eq(r.endDate,l,"minutes")||!1===n({start:i,end:l,resourceId:e.props.resource}))||e.state.start===a.start&&e.state.end===a.end&&e.state.selecting===a.selecting||e.setState(a)},u=function(n){var r=e.slotMetrics.closestSlotFromPoint(n,gb(t));e.state.selecting||(e._initialSlot=r);var a=e._initialSlot;o.lte(a,r)?r=e.slotMetrics.nextSlot(r):o.gt(a,r)&&(a=e.slotMetrics.nextSlot(a));var l=e.slotMetrics.getRange(o.min(a,r),o.max(a,r));return i(i({},l),{},{selecting:!0,top:"".concat(l.top,"%"),height:"".concat(l.height,"%")})},s=function(t,n){if(!db(e.containerRef.current,t)){var r=u(t),o=r.startDate,a=r.endDate;e._selectSlot({startDate:o,endDate:a,action:n,box:t})}e.setState({selecting:!1})};a.on("selecting",l),a.on("selectStart",l),a.on("beforeSelect",(function(t){if("ignoreEvents"===e.props.selectable)return!db(e.containerRef.current,t)})),a.on("click",(function(e){return s(e,"click")})),a.on("doubleClick",(function(e){return s(e,"doubleClick")})),a.on("select",(function(t){e.state.selecting&&(e._selectSlot(i(i({},e.state),{},{action:"select",bounds:t})),e.setState({selecting:!1}))})),a.on("reset",(function(){e.state.selecting&&e.setState({selecting:!1})}))},e._teardownSelectable=function(){e._selector&&(e._selector.teardown(),e._selector=null)},e._selectSlot=function(t){for(var n=t.startDate,r=t.endDate,o=t.action,a=t.bounds,i=t.box,l=n,u=[];e.props.localizer.lte(l,r);)u.push(l),l=new Date(+l+60*e.props.step*1e3);Lt(e.props.onSelectSlot,{slots:u,start:n,end:r,resourceId:e.props.resource,action:o,bounds:a,box:i})},e._select=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onSelectEvent,n)},e._doubleClick=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onDoubleClickEvent,n)},e._keyPress=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onKeyPressEvent,n)},e.slotMetrics=bk(e.props),e.containerRef=O.createRef(),e}return g(t,e),f(t,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable(),this.props.isNow&&this.setTimeIndicatorPositionUpdateInterval()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable(),this.clearTimeIndicatorInterval()}},{key:"componentDidUpdate",value:function(e,t){this.props.selectable&&!e.selectable&&this._selectable(),!this.props.selectable&&e.selectable&&this._teardownSelectable();var n=this.props,r=n.getNow,o=n.isNow,a=n.localizer,i=n.date,l=n.min,u=n.max,s=a.neq(e.getNow(),r(),"minutes");if(e.isNow!==o||s){if(this.clearTimeIndicatorInterval(),o){var c=!s&&a.eq(e.date,i,"minutes")&&t.timeIndicatorPosition===this.state.timeIndicatorPosition;this.setTimeIndicatorPositionUpdateInterval(c)}}else o&&(a.neq(e.min,l,"minutes")||a.neq(e.max,u,"minutes"))&&this.positionTimeIndicator()}},{key:"setTimeIndicatorPositionUpdateInterval",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.intervalTriggered||t||this.positionTimeIndicator(),this._timeIndicatorTimeout=window.setTimeout((function(){e.intervalTriggered=!0,e.positionTimeIndicator(),e.setTimeIndicatorPositionUpdateInterval()}),6e4)}},{key:"clearTimeIndicatorInterval",value:function(){this.intervalTriggered=!1,window.clearTimeout(this._timeIndicatorTimeout)}},{key:"positionTimeIndicator",value:function(){var e=this.props,t=e.min,n=e.max,r=(0,e.getNow)();if(r>=t&&r<=n){var o=this.slotMetrics.getCurrentTimePosition(r);this.intervalTriggered=!0,this.setState({timeIndicatorPosition:o})}else this.clearTimeIndicatorInterval()}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.max,r=e.rtl,o=e.isNow,a=e.resource,i=e.accessors,l=e.localizer,s=e.getters,c=s.dayProp,f=u(s,wE),d=e.components,p=d.eventContainerWrapper,v=u(d,kE);this.slotMetrics=this.slotMetrics.update(this.props);var h=this.slotMetrics,m=this.state,g=m.selecting,y=m.top,b=m.height,w={start:m.startDate,end:m.endDate},k=c(n,a),E=k.className,S=k.style,_=v.dayColumnWrapper||bE;return ce.createElement(_,{ref:this.containerRef,date:t,style:S,className:D(E,"rbc-day-slot","rbc-time-column",o&&"rbc-now",o&&"rbc-today",g&&"rbc-slot-selecting"),slotMetrics:h,resource:a},h.groups.map((function(e,t){return ce.createElement(hE,{key:t,group:e,resource:a,getters:f,components:v})})),ce.createElement(p,{localizer:l,resource:a,accessors:i,getters:f,components:v,slotMetrics:h},ce.createElement("div",{className:D("rbc-events-container",r&&"rtl")},this.renderEvents({events:this.props.backgroundEvents,isBackgroundEvent:!0}),this.renderEvents({events:this.props.events}))),g&&ce.createElement("div",{className:"rbc-slot-selection",style:{top:y,height:b}},ce.createElement("span",null,l.format(w,"selectRangeFormat"))),o&&this.intervalTriggered&&ce.createElement("div",{className:"rbc-current-time-indicator",style:{top:"".concat(this.state.timeIndicatorPosition,"%")}}))}}])}(ce.Component);EE.defaultProps={dragThroughEvents:!0,timeslots:2};var SE=function(e){var t=e.label;return ce.createElement(ce.Fragment,null,t)},DE=function(e){function t(){var e;s(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=h(this,t,[].concat(r))).handleHeaderClick=function(t,n,r){r.preventDefault(),Lt(e.props.onDrillDown,[t,n])},e.renderRow=function(t){var n=e.props,r=n.events,o=n.rtl,a=n.selectable,i=n.getNow,l=n.range,u=n.getters,s=n.localizer,c=n.accessors,f=n.components,d=n.resizable,p=c.resourceId(t),v=t?r.filter((function(e){return c.resource(e)===p})):r;return ce.createElement(dk,{isAllDay:!0,rtl:o,getNow:i,minRows:2,maxRows:e.props.allDayMaxRows+1,range:l,events:v,resourceId:p,className:"rbc-allday-cell",selectable:a,selected:e.props.selected,components:f,accessors:c,getters:u,localizer:s,onSelect:e.props.onSelectEvent,onShowMore:e.props.onShowMore,onDoubleClick:e.props.onDoubleClickEvent,onKeyPress:e.props.onKeyPressEvent,onSelectSlot:e.props.onSelectSlot,longPressThreshold:e.props.longPressThreshold,resizable:d})},e}return g(t,e),f(t,[{key:"renderHeaderCells",value:function(e){var t=this,n=this.props,r=n.localizer,o=n.getDrilldownView,a=n.getNow,i=n.getters.dayProp,l=n.components.header,u=void 0===l?pk:l,s=a();return e.map((function(e,n){var a=o(e),l=r.format(e,"dayFormat"),c=i(e),f=c.className,d=c.style,p=ce.createElement(u,{date:e,label:l,localizer:r});return ce.createElement("div",{key:n,style:d,className:D("rbc-header",f,r.isSameDate(e,s)&&"rbc-today")},a?ce.createElement("button",{type:"button",className:"rbc-button-link",onClick:function(n){return t.handleHeaderClick(e,a,n)}},p):ce.createElement("span",null,p))}))}},{key:"render",value:function(){var e=this,t=this.props,n=t.width,r=t.rtl,o=t.resources,a=t.range,i=t.events,l=t.getNow,u=t.accessors,s=t.selectable,c=t.components,f=t.getters,d=t.scrollRef,p=t.localizer,v=t.isOverflowing,h=t.components,m=h.timeGutterHeader,g=h.resourceHeader,y=void 0===g?SE:g,b=t.resizable,w={};v&&(w[r?"marginLeft":"marginRight"]="".concat(ch()-1,"px"));var k=o.groupEvents(i);return ce.createElement("div",{style:w,ref:d,className:D("rbc-time-header",v&&"rbc-overflowing")},ce.createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:n,minWidth:n,maxWidth:n}},m&&ce.createElement(m,null)),o.map((function(t,n){var o=E(t,2),i=o[0],d=o[1];return ce.createElement("div",{className:"rbc-time-header-content",key:i||n},d&&ce.createElement("div",{className:"rbc-row rbc-row-resource",key:"resource_".concat(n)},ce.createElement("div",{className:"rbc-header"},ce.createElement(y,{index:n,label:u.resourceTitle(d),resource:d}))),ce.createElement("div",{className:"rbc-row rbc-time-header-cell".concat(a.length<=1?" rbc-time-header-cell-single-day":"")},e.renderHeaderCells(a)),ce.createElement(dk,{isAllDay:!0,rtl:r,getNow:l,minRows:2,maxRows:e.props.allDayMaxRows+1,range:a,events:k.get(i)||[],resourceId:d&&i,className:"rbc-allday-cell",selectable:s,selected:e.props.selected,components:c,accessors:u,getters:f,localizer:p,onSelect:e.props.onSelectEvent,onShowMore:e.props.onShowMore,onDoubleClick:e.props.onDoubleClickEvent,onKeyDown:e.props.onKeyPressEvent,onSelectSlot:e.props.onSelectSlot,longPressThreshold:e.props.longPressThreshold,resizable:b}))})))}}])}(ce.Component),_E=function(e){function t(){var e;s(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=h(this,t,[].concat(r))).handleHeaderClick=function(t,n,r){r.preventDefault(),Lt(e.props.onDrillDown,[t,n])},e}return g(t,e),f(t,[{key:"renderHeaderCells",value:function(e){var t=this,n=this.props,r=n.localizer,o=n.getDrilldownView,a=n.getNow,i=n.getters.dayProp,l=n.components,u=l.header,s=void 0===u?pk:u,c=l.resourceHeader,f=void 0===c?SE:c,d=n.resources,p=n.accessors,v=n.events,h=n.rtl,m=n.selectable,g=n.components,y=n.getters,b=n.resizable,w=a(),k=d.groupEvents(v);return e.map((function(n,l){var u=o(n),c=r.format(n,"dayFormat"),v=i(n),S=v.className,_=v.style,x=ce.createElement(s,{date:n,label:c,localizer:r});return ce.createElement("div",{key:l,className:"rbc-time-header-content rbc-resource-grouping"},ce.createElement("div",{className:"rbc-row rbc-time-header-cell".concat(e.length<=1?" rbc-time-header-cell-single-day":"")},ce.createElement("div",{style:_,className:D("rbc-header",S,r.isSameDate(n,w)&&"rbc-today")},u?ce.createElement("button",{type:"button",className:"rbc-button-link",onClick:function(e){return t.handleHeaderClick(n,u,e)}},x):ce.createElement("span",null,x))),ce.createElement("div",{className:"rbc-row"},d.map((function(e,t){var o=E(e,2),a=o[0],i=o[1];return ce.createElement("div",{key:"resource_".concat(a,"_").concat(t),className:D("rbc-header",S,r.isSameDate(n,w)&&"rbc-today")},ce.createElement(f,{index:t,label:p.resourceTitle(i),resource:i}))}))),ce.createElement("div",{className:"rbc-row rbc-m-b-negative-3 rbc-h-full"},d.map((function(e,o){var i=E(e,2),l=i[0],u=i[1],s=(k.get(l)||[]).filter((function(e){return r.isSameDate(e.start,n)||r.isSameDate(e.end,n)}));return ce.createElement(dk,{key:"resource_".concat(l,"_").concat(o),isAllDay:!0,rtl:h,getNow:a,minRows:2,maxRows:t.props.allDayMaxRows+1,range:[n],events:s,resourceId:u&&l,className:"rbc-allday-cell",selectable:m,selected:t.props.selected,components:g,accessors:p,getters:y,localizer:r,onSelect:t.props.onSelectEvent,onShowMore:t.props.onShowMore,onDoubleClick:t.props.onDoubleClickEvent,onKeyDown:t.props.onKeyPressEvent,onSelectSlot:t.props.onSelectSlot,longPressThreshold:t.props.longPressThreshold,resizable:b})}))))}))}},{key:"render",value:function(){var e=this.props,t=e.width,n=e.rtl,r=e.range,o=e.scrollRef,a=e.isOverflowing,i=e.components.timeGutterHeader,l={};return a&&(l[n?"marginLeft":"marginRight"]="".concat(ch()-1,"px")),ce.createElement("div",{style:l,ref:o,className:D("rbc-time-header",a&&"rbc-overflowing")},ce.createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:t,minWidth:t,maxWidth:t}},i&&ce.createElement(i,null)),this.renderHeaderCells(r))}}])}(ce.Component);var xE=function(e){var t=e.min,n=e.max,r=e.timeslots,o=e.step,a=e.localizer,i=e.getNow,l=e.resource,u=e.components,s=e.getters,c=e.gutterRef,f=u.timeGutterWrapper,d=O.useMemo((function(){return function(e){var t=e.min,n=e.max,r=e.localizer;return r.getTimezoneOffset(t)!==r.getTimezoneOffset(n)?{start:r.add(t,-1,"day"),end:r.add(n,-1,"day")}:{start:t,end:n}}({min:t,max:n,localizer:a})}),[null==t?void 0:t.toISOString(),null==n?void 0:n.toISOString(),a]),p=d.start,v=d.end,h=E(O.useState(bk({min:p,max:v,timeslots:r,step:o,localizer:a})),2),m=h[0],g=h[1];O.useEffect((function(){m&&g(m.update({min:p,max:v,timeslots:r,step:o,localizer:a}))}),[null==p?void 0:p.toISOString(),null==v?void 0:v.toISOString(),r,o]);var y=O.useCallback((function(e,t){if(t)return null;var n=m.dateIsInGroup(i(),t);return ce.createElement("span",{className:D("rbc-label",n&&"rbc-now")},a.format(e,"timeGutterFormat"))}),[m,a,i]);return ce.createElement(f,{slotMetrics:m},ce.createElement("div",{className:"rbc-time-gutter rbc-time-column",ref:c},m.groups.map((function(e,t){return ce.createElement(hE,{key:t,group:e,resource:l,components:u,renderSlot:y,getters:s})}))))},OE=ce.forwardRef((function(e,t){return ce.createElement(xE,Object.assign({gutterRef:t},e))})),ME={};var CE=function(e){function t(e){var n;return s(this,t),(n=h(this,t,[e])).handleScroll=function(e){n.scrollRef.current&&(n.scrollRef.current.scrollLeft=e.target.scrollLeft)},n.handleResize=function(){fr(n.rafHandle),n.rafHandle=dr(n.checkOverflow)},n.handleKeyPressEvent=function(){n.clearSelection();for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];Lt(n.props.onKeyPressEvent,t)},n.handleSelectEvent=function(){n.clearSelection();for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];Lt(n.props.onSelectEvent,t)},n.handleDoubleClickEvent=function(){n.clearSelection();for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];Lt(n.props.onDoubleClickEvent,t)},n.handleShowMore=function(e,t,r,o,a){var l=n.props,u=l.popup,s=l.onDrillDown,c=l.onShowMore,f=l.getDrilldownView,d=l.doShowMoreDrillDown;if(n.clearSelection(),u){var p=or(r,n.containerRef.current);n.setState({overlay:{date:t,events:e,position:i(i({},p),{},{width:"200px"}),target:a}})}else d&&Lt(s,[t,f(t)||De.DAY]);Lt(c,[e,t,o])},n.handleSelectAllDaySlot=function(e,t){var r=n.props.onSelectSlot,o=new Date(e[0]),a=new Date(e[e.length-1]);a.setDate(e[e.length-1].getDate()+1),Lt(r,{slots:e,start:o,end:a,action:t.action,resourceId:t.resourceId})},n.overlayDisplay=function(){n.setState({overlay:null})},n.checkOverflow=function(){if(!n._updatingOverflow){var e=n.contentRef.current;if(null!=e&&e.scrollHeight){var t=e.scrollHeight>e.clientHeight;n.state.isOverflowing!==t&&(n._updatingOverflow=!0,n.setState({isOverflowing:t},(function(){n._updatingOverflow=!1})))}}},n.memoizedResources=sk((function(e,t){return function(e,t){return{map:function(n){return e?e.map((function(e,r){return n([t.resourceId(e),e],r)})):[n([ME,null],0)]},groupEvents:function(n){var r=new Map;return e?(n.forEach((function(e){var n=t.resource(e)||ME;if(Array.isArray(n))n.forEach((function(t){var n=r.get(t)||[];n.push(e),r.set(t,n)}));else{var o=r.get(n)||[];o.push(e),r.set(n,o)}})),r):(r.set(ME,n),r)}}}(e,t)})),n.state={gutterWidth:void 0,isOverflowing:null},n.scrollRef=ce.createRef(),n.contentRef=ce.createRef(),n.containerRef=ce.createRef(),n._scrollRatio=null,n.gutterRef=O.createRef(),n}return g(t,e),f(t,[{key:"getSnapshotBeforeUpdate",value:function(){return this.checkOverflow(),null}},{key:"componentDidMount",value:function(){null==this.props.width&&this.measureGutter(),this.calculateScroll(),this.applyScroll(),window.addEventListener("resize",this.handleResize)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.handleResize),fr(this.rafHandle),this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest)}},{key:"componentDidUpdate",value:function(){this.applyScroll()}},{key:"renderDayColumn",value:function(e,t,n,r,o,a,i,l,u,s){var c=this.props,f=c.min,d=c.max,p=(r.get(t)||[]).filter((function(t){return a.inRange(e,i.start(t),i.end(t),"day")})),v=(o.get(t)||[]).filter((function(t){return a.inRange(e,i.start(t),i.end(t),"day")}));return ce.createElement(EE,Object.assign({},this.props,{localizer:a,min:a.merge(e,f),max:a.merge(e,d),resource:n&&t,components:l,isNow:a.isSameDate(e,s),key:"".concat(t,"-").concat(e),date:e,events:p,backgroundEvents:v,dayLayoutAlgorithm:u}))}},{key:"renderResourcesFirst",value:function(e,t,n,r,o,a,i,l,u){var s=this;return t.map((function(t){var c=E(t,2),f=c[0],d=c[1];return e.map((function(e){return s.renderDayColumn(e,f,d,n,r,o,a,l,u,i)}))}))}},{key:"renderRangeFirst",value:function(e,t,n,r,o,a,i,l,u){var s=this;return e.map((function(e){return ce.createElement("div",{style:{display:"flex",minHeight:"100%",flex:1},key:e},t.map((function(t){var c=E(t,2),f=c[0],d=c[1];return ce.createElement("div",{style:{flex:1},key:a.resourceId(d)},s.renderDayColumn(e,f,d,n,r,o,a,l,u,i))})))}))}},{key:"renderEvents",value:function(e,t,n,r){var o=this.props,a=o.accessors,i=o.localizer,l=o.resourceGroupingLayout,u=o.components,s=o.dayLayoutAlgorithm,c=this.memoizedResources(this.props.resources,a),f=c.groupEvents(t),d=c.groupEvents(n);return l?this.renderRangeFirst(e,c,f,d,i,a,r,u,s):this.renderResourcesFirst(e,c,f,d,i,a,r,u,s)}},{key:"render",value:function(){var e,t=this.props,n=t.events,r=t.backgroundEvents,o=t.range,a=t.width,i=t.rtl,l=t.selected,u=t.getNow,s=t.resources,c=t.components,f=t.accessors,d=t.getters,p=t.localizer,v=t.min,h=t.max,m=t.showMultiDayTimes,g=t.longPressThreshold,y=t.resizable,b=t.resourceGroupingLayout;a=a||this.state.gutterWidth;var w=o[0],k=o[o.length-1];this.slots=o.length;var E=[],S=[],_=[];n.forEach((function(e){if(Yw(e,w,k,f,p)){var t=f.start(e),n=f.end(e);f.allDay(e)||p.startAndEndAreDateOnly(t,n)||!m&&!p.isSameDate(t,n)?E.push(e):S.push(e)}})),r.forEach((function(e){Yw(e,w,k,f,p)&&_.push(e)})),E.sort((function(e,t){return Kw(e,t,f,p)}));var x={range:o,events:E,width:a,rtl:i,getNow:u,localizer:p,selected:l,allDayMaxRows:this.props.showAllEvents?1/0:null!==(e=this.props.allDayMaxRows)&&void 0!==e?e:1/0,resources:this.memoizedResources(s,f),selectable:this.props.selectable,accessors:f,getters:d,components:c,scrollRef:this.scrollRef,isOverflowing:this.state.isOverflowing,longPressThreshold:g,onSelectSlot:this.handleSelectAllDaySlot,onSelectEvent:this.handleSelectEvent,onShowMore:this.handleShowMore,onDoubleClickEvent:this.props.onDoubleClickEvent,onKeyPressEvent:this.props.onKeyPressEvent,onDrillDown:this.props.onDrillDown,getDrilldownView:this.props.getDrilldownView,resizable:y};return ce.createElement("div",{className:D("rbc-time-view",s&&"rbc-time-view-resources"),ref:this.containerRef},s&&s.length>1&&b?ce.createElement(_E,x):ce.createElement(DE,x),this.props.popup&&this.renderOverlay(),ce.createElement("div",{ref:this.contentRef,className:"rbc-time-content",onScroll:this.handleScroll},ce.createElement(OE,{date:w,ref:this.gutterRef,localizer:p,min:p.merge(w,v),max:p.merge(w,h),step:this.props.step,getNow:this.props.getNow,timeslots:this.props.timeslots,components:c,className:"rbc-time-gutter",getters:d}),this.renderEvents(o,S,_,u())))}},{key:"renderOverlay",value:function(){var e,t,n=this,r=null!==(e=null===(t=this.state)||void 0===t?void 0:t.overlay)&&void 0!==e?e:{},o=this.props,a=o.accessors,i=o.localizer,l=o.components,u=o.getters,s=o.selected,c=o.popupOffset,f=o.handleDragStart;return ce.createElement(ub,{overlay:r,accessors:a,localizer:i,components:l,getters:u,selected:s,popupOffset:c,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:f,show:!!r.position,overlayDisplay:this.overlayDisplay,onHide:function(){return n.setState({overlay:null})}})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}},{key:"measureGutter",value:function(){var e=this;this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest),this.measureGutterAnimationFrameRequest=window.requestAnimationFrame((function(){var t,n=null!==(t=e.gutterRef)&&void 0!==t&&t.current?gk(e.gutterRef.current):void 0;n&&e.state.gutterWidth!==n&&e.setState({gutterWidth:n})}))}},{key:"applyScroll",value:function(){if(null!=this._scrollRatio&&!0===this.props.enableAutoScroll){var e=this.contentRef.current;e.scrollTop=e.scrollHeight*this._scrollRatio,this._scrollRatio=null}}},{key:"calculateScroll",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=e.min,n=e.max,r=e.scrollToTime,o=e.localizer,a=o.diff(o.merge(r,t),r,"milliseconds"),i=o.diff(t,n,"milliseconds");this._scrollRatio=a/i}}])}(O.Component);CE.defaultProps={step:30,timeslots:2,resourceGroupingLayout:!1};var TE=["date","localizer","min","max","scrollToTime","enableAutoScroll"],PE=function(e){function t(){return s(this,t),h(this,t,arguments)}return g(t,e),f(t,[{key:"render",value:function(){var e=this.props,n=e.date,r=e.localizer,o=e.min,a=void 0===o?r.startOf(new Date,"day"):o,i=e.max,l=void 0===i?r.endOf(new Date,"day"):i,s=e.scrollToTime,c=void 0===s?r.startOf(new Date,"day"):s,f=e.enableAutoScroll,d=void 0===f||f,p=u(e,TE),v=t.range(n,{localizer:r});return ce.createElement(CE,Object.assign({},p,{range:v,eventOffset:10,localizer:r,min:a,max:l,scrollToTime:c,enableAutoScroll:d}))}}])}(ce.Component);function RE(e){return y(e)||jt(e)||w(e)||k()}PE.range=function(e,t){return[t.localizer.startOf(e,"day")]},PE.navigate=function(e,t,n){var r=n.localizer;switch(t){case Se.PREVIOUS:return r.add(e,-1,"day");case Se.NEXT:return r.add(e,1,"day");default:return e}},PE.title=function(e,t){return t.localizer.format(e,"dayHeaderFormat")};var NE=["date","localizer","min","max","scrollToTime","enableAutoScroll"],zE=function(e){function t(){return s(this,t),h(this,t,arguments)}return g(t,e),f(t,[{key:"render",value:function(){var e=this.props,n=e.date,r=e.localizer,o=e.min,a=void 0===o?r.startOf(new Date,"day"):o,i=e.max,l=void 0===i?r.endOf(new Date,"day"):i,s=e.scrollToTime,c=void 0===s?r.startOf(new Date,"day"):s,f=e.enableAutoScroll,d=void 0===f||f,p=u(e,NE),v=t.range(n,this.props);return ce.createElement(CE,Object.assign({},p,{range:v,eventOffset:15,localizer:r,min:a,max:l,scrollToTime:c,enableAutoScroll:d}))}}])}(ce.Component);zE.defaultProps=CE.defaultProps,zE.navigate=function(e,t,n){var r=n.localizer;switch(t){case Se.PREVIOUS:return r.add(e,-1,"week");case Se.NEXT:return r.add(e,1,"week");default:return e}},zE.range=function(e,t){var n=t.localizer,r=n.startOfWeek(),o=n.startOf(e,"week",r),a=n.endOf(e,"week",r);return n.range(o,a)},zE.title=function(e,t){var n=t.localizer,r=RE(zE.range(e,{localizer:n})),o=r[0],a=r.slice(1);return n.format({start:o,end:a.pop()},"dayRangeHeaderFormat")};var LE=["date","localizer","min","max","scrollToTime","enableAutoScroll"];function AE(e,t){return zE.range(e,t).filter((function(e){return-1===[6,0].indexOf(e.getDay())}))}var jE=function(e){function t(){return s(this,t),h(this,t,arguments)}return g(t,e),f(t,[{key:"render",value:function(){var e=this.props,t=e.date,n=e.localizer,r=e.min,o=void 0===r?n.startOf(new Date,"day"):r,a=e.max,i=void 0===a?n.endOf(new Date,"day"):a,l=e.scrollToTime,s=void 0===l?n.startOf(new Date,"day"):l,c=e.enableAutoScroll,f=void 0===c||c,d=u(e,LE),p=AE(t,this.props);return ce.createElement(CE,Object.assign({},d,{range:p,eventOffset:15,localizer:n,min:o,max:i,scrollToTime:s,enableAutoScroll:f}))}}])}(ce.Component);jE.defaultProps=CE.defaultProps,jE.range=AE,jE.navigate=zE.navigate,jE.title=function(e,t){var n=t.localizer,r=RE(AE(e,{localizer:n})),o=r[0],a=r.slice(1);return n.format({start:o,end:a.pop()},"dayRangeHeaderFormat")};function FE(e){var t=e.accessors,n=e.components,r=e.date,o=e.events,a=e.getters,i=e.length,l=void 0===i?30:i,u=e.localizer,s=e.onDoubleClickEvent,c=e.onSelectEvent,f=e.selected,d=O.useRef(null),p=O.useRef(null),v=O.useRef(null),h=O.useRef(null),m=O.useRef(null);O.useEffect((function(){y()}));var g=function(e,r){var o="",a=n.time,i=u.messages.allDay,l=t.end(r),s=t.start(r);return t.allDay(r)||(u.eq(s,l)?i=u.format(s,"agendaTimeFormat"):u.isSameDate(s,l)?i=u.format({start:s,end:l},"agendaTimeRangeFormat"):u.isSameDate(e,s)?i=u.format(s,"agendaTimeFormat"):u.isSameDate(e,l)&&(i=u.format(l,"agendaTimeFormat"))),u.gt(e,s,"day")&&(o="rbc-continues-prior"),u.lt(e,l,"day")&&(o+=" rbc-continues-after"),ce.createElement("span",{className:o.trim()},a?ce.createElement(a,{event:r,day:e,label:i}):i)},y=function(){if(m.current){var e=d.current,t=m.current.firstChild;if(t){var n,r,o=h.current.scrollHeight>h.current.clientHeight,a=[],i=a;a=[gk(t.children[0]),gk(t.children[1])],i[0]===a[0]&&i[1]===a[1]||(p.current.style.width=a[0]+"px",v.current.style.width=a[1]+"px"),o?(r="rbc-header-overflowing",(n=e).classList?n.classList.add(r):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)),e.style.marginRight=ch()+"px"):function(e,t){e.classList?e.classList.remove(t):"string"==typeof e.className?e.className=sh(e.className,t):e.setAttribute("class",sh(e.className&&e.className.baseVal||"",t))}(e,"rbc-header-overflowing")}}},b=u.messages,w=u.add(r,l,"day"),k=u.range(r,w,"day");return(o=o.filter((function(e){return Yw(e,u.startOf(r,"day"),u.endOf(w,"day"),t,u)}))).sort((function(e,n){return+t.start(e)-+t.start(n)})),ce.createElement("div",{className:"rbc-agenda-view"},0!==o.length?ce.createElement(ce.Fragment,null,ce.createElement("table",{ref:d,className:"rbc-agenda-table"},ce.createElement("thead",null,ce.createElement("tr",null,ce.createElement("th",{className:"rbc-header",ref:p},b.date),ce.createElement("th",{className:"rbc-header",ref:v},b.time),ce.createElement("th",{className:"rbc-header"},b.event)))),ce.createElement("div",{className:"rbc-agenda-content",ref:h},ce.createElement("table",{className:"rbc-agenda-table"},ce.createElement("tbody",{ref:m},k.map((function(e,r){return function(e,r,o){var i=n.event,l=n.date;return(r=r.filter((function(n){return Yw(n,u.startOf(e,"day"),u.endOf(e,"day"),t,u)}))).map((function(n,d){var p=t.title(n),v=t.end(n),h=t.start(n),m=a.eventProp(n,h,v,nb(n,f)),y=0===d&&u.format(e,"agendaDateFormat"),b=0===d&&ce.createElement("td",{rowSpan:r.length,className:"rbc-agenda-date-cell"},l?ce.createElement(l,{day:e,label:y}):y);return ce.createElement("tr",{key:o+"_"+d,className:m.className,style:m.style},b,ce.createElement("td",{className:"rbc-agenda-time-cell"},g(e,n)),ce.createElement("td",{className:"rbc-agenda-event-cell",onClick:function(e){return c&&c(n,e)},onDoubleClick:function(e){return s&&s(n,e)}},i?ce.createElement(i,{event:n,title:p}):p))}),[])}(e,o,r)})))))):ce.createElement("span",{className:"rbc-agenda-empty"},b.noEventsInRange))}FE.range=function(e,t){var n=t.length,r=void 0===n?30:n;return{start:e,end:t.localizer.add(e,r,"day")}},FE.navigate=function(e,t,n){var r=n.length,o=void 0===r?30:r,a=n.localizer;switch(t){case Se.PREVIOUS:return a.add(e,-o,"day");case Se.NEXT:return a.add(e,o,"day");default:return e}},FE.title=function(e,t){var n=t.length,r=void 0===n?30:n,o=t.localizer,a=o.add(e,r,"day");return o.format({start:e,end:a},"agendaHeaderFormat")};var IE=o(o(o(o(o({},De.MONTH,mk),De.WEEK,zE),De.WORK_WEEK,jE),De.DAY,PE),De.AGENDA,FE),UE=["action","date","today"];function WE(e,t){var n=t.action,r=t.date,o=t.today,a=u(t,UE);switch(e="string"==typeof e?IE[e]:e,n){case Se.TODAY:r=o||new Date;break;case Se.DATE:break;default:de(e&&"function"==typeof e.navigate,"Calendar View components must implement a static `.navigate(date, action)` method.s"),r=e.navigate(r,n,a)}return r}var HE=rn,VE=ry,BE=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t},$E=Object.prototype.hasOwnProperty;var YE=ty,qE=function(e){if(!HE(e))return BE(e);var t=VE(e),n=[];for(var r in e)("constructor"!=r||!t&&$E.call(e,r))&&n.push(r);return n},KE=fn;var QE=function(e){return KE(e)?YE(e,!0):qE(e)},GE=aE,XE=Ut,JE=yn,ZE=QE,eS=Object.prototype,tS=eS.hasOwnProperty,nS=GE((function(e,t){e=Object(e);var n=-1,r=t.length,o=r>2?t[2]:void 0;for(o&&JE(t[0],t[1],o)&&(r=1);++n<r;)for(var a=t[n],i=ZE(a),l=-1,u=i.length;++l<u;){var s=i[l],c=e[s];(void 0===c||XE(c,eS[s])&&!tS.call(e,s))&&(e[s]=a[s])}return e})),rS=Qk;var oS=function(e,t,n){"__proto__"==t&&rS?rS(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},aS=oS,iS=Mk,lS=Fw;var uS=function(e,t){var n={};return t=lS(t),iS(e,(function(e,r,o){aS(n,r,t(e,r,o))})),n};var sS=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e},cS=oS,fS=Ut,dS=Object.prototype.hasOwnProperty;var pS=function(e,t,n){var r=e[t];dS.call(e,t)&&fS(r,n)&&(void 0!==n||t in e)||cS(e,t,n)},vS=pS,hS=oS;var mS=function(e,t,n,r){var o=!n;n||(n={});for(var a=-1,i=t.length;++a<i;){var l=t[a],u=r?r(n[l],e[l],l,n,e):void 0;void 0===u&&(u=e[l]),o?hS(n,l,u):vS(n,l,u)}return n},gS=mS,yS=dy;var bS=function(e,t){return e&&gS(t,yS(t),e)},wS=mS,kS=QE;var ES=function(e,t){return e&&wS(t,kS(t),e)},SS={},DS={get exports(){return SS},set exports(e){SS=e}};!function(e,t){var n=Bt,r=t&&!t.nodeType&&t,o=r&&e&&!e.nodeType&&e,a=o&&o.exports===r?n.Buffer:void 0,i=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,r=i?i(n):new e.constructor(n);return e.copy(r),r}}(DS,SS);var _S=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t},xS=mS,OS=Eg;var MS=function(e,t){return xS(e,OS(e),t)},CS=oy(Object.getPrototypeOf,Object),TS=dg,PS=CS,RS=Eg,NS=gg,zS=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)TS(t,RS(e)),e=PS(e);return t}:NS,LS=mS,AS=zS;var jS=function(e,t){return LS(e,AS(e),t)},FS=mg,IS=zS,US=QE;var WS=function(e){return FS(e,US,IS)},HS=Object.prototype.hasOwnProperty;var VS=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&HS.call(e,"index")&&(n.index=e.index,n.input=e.input),n},BS=rg;var $S=function(e){var t=new e.constructor(e.byteLength);return new BS(t).set(new BS(e)),t},YS=$S;var qS=function(e,t){var n=t?YS(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)},KS=/\w*$/;var QS=function(e){var t=new e.constructor(e.source,KS.exec(e));return t.lastIndex=e.lastIndex,t},GS=$t?$t.prototype:void 0,XS=GS?GS.valueOf:void 0;var JS=$S;var ZS=$S,eD=qS,tD=QS,nD=function(e){return XS?Object(XS.call(e)):{}},rD=function(e,t){var n=t?JS(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)};var oD=function(e,t,n){var r=e.constructor;switch(t){case"[object ArrayBuffer]":return ZS(e);case"[object Boolean]":case"[object Date]":return new r(+e);case"[object DataView]":return eD(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return rD(e,n);case"[object Map]":case"[object Set]":return new r;case"[object Number]":case"[object String]":return new r(e);case"[object RegExp]":return tD(e);case"[object Symbol]":return nD(e)}},aD=rn,iD=Object.create,lD=function(){function e(){}return function(t){if(!aD(t))return{};if(iD)return iD(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}(),uD=lD,sD=CS,cD=ry;var fD=function(e){return"function"!=typeof e.constructor||cD(e)?{}:uD(sD(e))},dD=Fy,pD=En;var vD=function(e){return pD(e)&&"[object Map]"==dD(e)},hD=Wg,mD=Hg&&Hg.isMap,gD=mD?hD(mD):vD,yD=Fy,bD=En;var wD=function(e){return bD(e)&&"[object Set]"==yD(e)},kD=Wg,ED=Hg&&Hg.isSet,SD=ED?kD(ED):wD,DD=Km,_D=sS,xD=pS,OD=bS,MD=ES,CD=SS,TD=_S,PD=MS,RD=jS,ND=my,zD=WS,LD=Fy,AD=VS,jD=oD,FD=fD,ID=pg,UD=Ng,WD=gD,HD=rn,VD=SD,BD=dy,$D=QE,YD="[object Arguments]",qD="[object Function]",KD="[object Object]",QD={};QD[YD]=QD["[object Array]"]=QD["[object ArrayBuffer]"]=QD["[object DataView]"]=QD["[object Boolean]"]=QD["[object Date]"]=QD["[object Float32Array]"]=QD["[object Float64Array]"]=QD["[object Int8Array]"]=QD["[object Int16Array]"]=QD["[object Int32Array]"]=QD["[object Map]"]=QD["[object Number]"]=QD[KD]=QD["[object RegExp]"]=QD["[object Set]"]=QD["[object String]"]=QD["[object Symbol]"]=QD["[object Uint8Array]"]=QD["[object Uint8ClampedArray]"]=QD["[object Uint16Array]"]=QD["[object Uint32Array]"]=!0,QD["[object Error]"]=QD[qD]=QD["[object WeakMap]"]=!1;var GD=function e(t,n,r,o,a,i){var l,u=1&n,s=2&n,c=4&n;if(r&&(l=a?r(t,o,a,i):r(t)),void 0!==l)return l;if(!HD(t))return t;var f=ID(t);if(f){if(l=AD(t),!u)return TD(t,l)}else{var d=LD(t),p=d==qD||"[object GeneratorFunction]"==d;if(UD(t))return CD(t,u);if(d==KD||d==YD||p&&!a){if(l=s||p?{}:FD(t),!u)return s?RD(t,MD(l,t)):PD(t,OD(l,t))}else{if(!QD[d])return a?t:{};l=jD(t,d,u)}}i||(i=new DD);var v=i.get(t);if(v)return v;i.set(t,l),VD(t)?t.forEach((function(o){l.add(e(o,n,r,o,t,i))})):WD(t)&&t.forEach((function(o,a){l.set(a,e(o,n,r,a,t,i))}));var h=f?void 0:(c?s?zD:ND:s?$D:BD)(t);return _D(h||t,(function(o,a){h&&(o=t[a=o]),xD(l,a,e(o,n,r,a,t,i))})),l};var XD=cw,JD=It;var ZD=aw,e_=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0},t_=function(e,t){return t.length<2?e:XD(e,JD(t,0,-1))},n_=lw;var r_=function(e,t){return t=ZD(t,e),null==(e=t_(e,t))||delete e[n_(e_(t))]},o_=nn,a_=CS,i_=En,l_=Function.prototype,u_=Object.prototype,s_=l_.toString,c_=u_.hasOwnProperty,f_=s_.call(Object);var d_=function(e){if(!i_(e)||"[object Object]"!=o_(e))return!1;var t=a_(e);if(null===t)return!0;var n=c_.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&s_.call(n)==f_};var p_=_k;var v_=function(e){return(null==e?0:e.length)?p_(e,1):[]},h_=Yk,m_=tE;var g_=qb,y_=GD,b_=r_,w_=aw,k_=mS,E_=function(e){return d_(e)?void 0:e},S_=WS,D_=function(e){return m_(h_(e,void 0,v_),e+"")}((function(e,t){var n={};if(null==e)return n;var r=!1;t=g_(t,(function(t){return t=w_(t,e),r||(r=t.length>1),t})),k_(e,S_(e),n),r&&(n=y_(n,7,E_));for(var o=t.length;o--;)b_(n,t[o]);return n})),__=sS,x_=lD,O_=Mk,M_=Fw,C_=CS,T_=pg,P_=Ng,R_=ln,N_=rn,z_=qg;var L_=function(e,t,n){var r=T_(e),o=r||P_(e)||z_(e);if(t=M_(t),null==n){var a=e&&e.constructor;n=o?r?new a:[]:N_(e)&&R_(a)?x_(C_(e)):{}}return(o?__:O_)(e,(function(e,r,o){return t(n,e,r,o)})),n};var A_=function(e){return function(t){return function(e,t){var r=null;return"function"==typeof t?r=t(e):"string"==typeof t&&"object"===n(e)&&null!=e&&t in e&&(r=e[t]),r}(t,e)}},j_=["view","date","getNow","onNavigate"],F_=["view","toolbar","events","backgroundEvents","resourceGroupingLayout","style","className","elementProps","date","getNow","length","showMultiDayTimes","onShowMore","doShowMoreDrillDown","components","formats","messages","culture"];function I_(e){if(Array.isArray(e))return e;for(var t=[],n=0,r=Object.entries(e);n<r.length;n++){var o=E(r[n],2),a=o[0];o[1]&&t.push(a)}return t}var U_=function(e){function r(){var e;s(this,r);for(var t=arguments.length,o=new Array(t),a=0;a<t;a++)o[a]=arguments[a];return(e=h(this,r,[].concat(o))).getViews=function(){var t=e.props.views;return Array.isArray(t)?L_(t,(function(e,t){return e[t]=IE[t]}),{}):"object"===n(t)?uS(t,(function(e,t){return!0===e?IE[t]:e})):IE},e.getView=function(){return e.getViews()[e.props.view]},e.getDrilldownView=function(t){var n=e.props,r=n.view,o=n.drilldownView,a=n.getDrilldownView;return a?a(t,r,Object.keys(e.getViews())):o},e.handleRangeChange=function(t,n,r){var o=e.props,a=o.onRangeChange,i=o.localizer;a&&n.range&&a(n.range(t,{localizer:i}),r)},e.handleNavigate=function(t,n){var r=e.props,o=r.view,a=r.date,l=r.getNow,s=r.onNavigate,c=u(r,j_),f=e.getView(),d=l();s(a=WE(f,i(i({},c),{},{action:t,date:n||a||d,today:d})),o,t),e.handleRangeChange(a,f)},e.handleViewChange=function(t){t!==e.props.view&&function(e,t){return-1!==I_(t.views).indexOf(e)}(t,e.props)&&e.props.onView(t);var n=e.getViews();e.handleRangeChange(e.props.date||e.props.getNow(),n[t],t)},e.handleSelectEvent=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onSelectEvent,n)},e.handleDoubleClickEvent=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onDoubleClickEvent,n)},e.handleKeyPressEvent=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Lt(e.props.onKeyPressEvent,n)},e.handleSelectSlot=function(t){Lt(e.props.onSelectSlot,t)},e.handleDrillDown=function(t,n){var r=e.props.onDrillDown;r?r(t,n,e.drilldownView):(n&&e.handleViewChange(n),e.handleNavigate(Se.DATE,t))},e.state={context:r.getContext(e.props)},e}return g(r,e),f(r,[{key:"render",value:function(){var e=this.props,t=e.view,n=e.toolbar,r=e.events,o=e.backgroundEvents,a=e.resourceGroupingLayout,i=e.style,l=e.className,s=e.elementProps,c=e.date,f=e.getNow,d=e.length,p=e.showMultiDayTimes,v=e.onShowMore,h=e.doShowMoreDrillDown;e.components,e.formats,e.messages,e.culture;var m=u(e,F_);c=c||f();var g=this.getView(),y=this.state.context,b=y.accessors,w=y.components,k=y.getters,E=y.localizer,S=y.viewNames,_=w.toolbar||zt,x=g.title(c,{localizer:E,length:d});return ce.createElement("div",Object.assign({},s,{className:D(l,"rbc-calendar",m.rtl&&"rbc-rtl"),style:i}),n&&ce.createElement(_,{date:c,view:t,views:S,label:x,onView:this.handleViewChange,onNavigate:this.handleNavigate,localizer:E}),ce.createElement(g,Object.assign({},m,{events:r,backgroundEvents:o,date:c,getNow:f,length:d,localizer:E,getters:k,components:w,accessors:b,showMultiDayTimes:p,getDrilldownView:this.getDrilldownView,onNavigate:this.handleNavigate,onDrillDown:this.handleDrillDown,onSelectEvent:this.handleSelectEvent,onDoubleClickEvent:this.handleDoubleClickEvent,onKeyPressEvent:this.handleKeyPressEvent,onSelectSlot:this.handleSelectSlot,onShowMore:v,doShowMoreDrillDown:h,resourceGroupingLayout:a})))}}],[{key:"getDerivedStateFromProps",value:function(e){return{context:r.getContext(e)}}},{key:"getContext",value:function(e){var n=e.startAccessor,r=e.endAccessor,o=e.allDayAccessor,a=e.tooltipAccessor,l=e.titleAccessor,u=e.resourceAccessor,s=e.resourceIdAccessor,c=e.resourceTitleAccessor,f=e.eventIdAccessor,d=e.eventPropGetter,p=e.backgroundEventPropGetter,v=e.slotPropGetter,h=e.slotGroupPropGetter,m=e.dayPropGetter,g=e.view,y=e.views,b=e.localizer,w=e.culture,k=e.messages,E=void 0===k?{}:k,S=e.components,D=void 0===S?{}:S,_=e.formats,x=void 0===_?{}:_,O=I_(y);return{viewNames:O,localizer:Nt(b,w,x,function(e){return i(i({},At),e)}(E)),getters:{eventProp:function(){return d&&d.apply(void 0,arguments)||{}},backgroundEventProp:function(){return p&&p.apply(void 0,arguments)||{}},slotProp:function(){return v&&v.apply(void 0,arguments)||{}},slotGroupProp:function(){return h&&h.apply(void 0,arguments)||{}},dayProp:function(){return m&&m.apply(void 0,arguments)||{}}},components:nS(D[g]||{},D_(D,O),{eventWrapper:t,backgroundEventWrapper:t,eventContainerWrapper:t,dateCellWrapper:t,weekWrapper:t,timeSlotWrapper:t,timeGutterWrapper:t}),accessors:{start:A_(n),end:A_(r),allDay:A_(o),tooltip:A_(a),title:A_(l),resource:A_(u),resourceId:A_(s),resourceTitle:A_(c),eventId:A_(f)}}}}])}(ce.Component);U_.defaultProps={events:[],backgroundEvents:[],elementProps:{},popup:!1,toolbar:!0,view:De.MONTH,views:[De.MONTH,De.WEEK,De.DAY,De.AGENDA],step:30,length:30,allDayMaxRows:1/0,doShowMoreDrillDown:!0,drilldownView:De.DAY,titleAccessor:"title",tooltipAccessor:"title",allDayAccessor:"allDay",startAccessor:"start",endAccessor:"end",resourceAccessor:"resourceId",resourceIdAccessor:"id",resourceTitleAccessor:"title",eventIdAccessor:"id",longPressThreshold:250,getNow:function(){return new Date},dayLayoutAlgorithm:"overlap"};var W_=function e(t,n,r){void 0===r&&(r=[]);var o,a=t.displayName||t.name||"Component",i=!!(o=t)&&("function"!=typeof o||o.prototype&&o.prototype.isReactComponent),u=Object.keys(n),s=u.map(he);!i&&r.length&&de(!1);var c=function(e){function o(){for(var t,o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];(t=e.call.apply(e,[this].concat(a))||this).handlers=Object.create(null),u.forEach((function(e){var r=n[e];t.handlers[r]=function(n){if(t.props[r]){var o;t._notifying=!0;for(var a=arguments.length,i=new Array(a>1?a-1:0),l=1;l<a;l++)i[l-1]=arguments[l];(o=t.props)[r].apply(o,[n].concat(i)),t._notifying=!1}t.unmounted||t.setState((function(t){var r,o=t.values;return{values:fe(Object.create(null),o,(r={},r[e]=n,r))}}))}})),r.length&&(t.attachRef=function(e){t.inner=e});var l=Object.create(null);return u.forEach((function(e){l[e]=t.props[he(e)]})),t.state={values:l,prevProps:{}},t}!function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,m(e,t)}(o,e);var a=o.prototype;return a.shouldComponentUpdate=function(){return!this._notifying},o.getDerivedStateFromProps=function(e,t){var n=t.values,r=t.prevProps,o={values:fe(Object.create(null),n),prevProps:{}};return u.forEach((function(t){o.prevProps[t]=e[t],!ve(e,t)&&ve(r,t)&&(o.values[t]=e[he(t)])})),o},a.componentWillUnmount=function(){this.unmounted=!0},a.render=function(){var e=this,n=this.props,r=n.innerRef,o=l(n,["innerRef"]);s.forEach((function(e){delete o[e]}));var a={};return u.forEach((function(t){var n=e.props[t];a[t]=void 0!==n?n:e.state.values[t]})),ce.createElement(t,fe({},o,a,this.handlers,{ref:r||this.attachRef}))},o}(ce.Component);!function(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var n=null,r=null,o=null;if("function"==typeof t.componentWillMount?n="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(n="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?r="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(r="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?o="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(o="UNSAFE_componentWillUpdate"),null!==n||null!==r||null!==o){var a=e.displayName||e.name,i="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+a+" uses "+i+" but also contains the following legacy lifecycles:"+(null!==n?"\n  "+n:"")+(null!==r?"\n  "+r:"")+(null!==o?"\n  "+o:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=me,t.componentWillReceiveProps=ge),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=ye;var l=t.componentDidUpdate;t.componentDidUpdate=function(e,t,n){var r=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;l.call(this,e,t,r)}}}(c),c.displayName="Uncontrolled("+a+")",c.propTypes=fe({innerRef:function(){}},function(e,t){var n={};return Object.keys(e).forEach((function(e){n[he(e)]=pe})),n}(n)),r.forEach((function(e){c.prototype[e]=function(){var t;return(t=this.inner)[e].apply(t,arguments)}}));var f=c;return ce.forwardRef&&((f=ce.forwardRef((function(e,t){return ce.createElement(c,fe({},e,{innerRef:t,__source:{fileName:"/Users/<USER>/src/uncontrollable/src/uncontrollable.js",lineNumber:128},__self:this}))}))).propTypes=c.propTypes),f.ControlledComponent=t,f.deferControlTo=function(t,r,o){return void 0===r&&(r={}),e(t,fe({},n,r),o)},f}(U_,{view:"onView",date:"onNavigate",selected:"onSelectEvent"}),H_=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"LT",t)+" – "+n.format(o,"LT",t)},V_={dateFormat:"DD",dayFormat:"DD ddd",weekdayFormat:"ddd",selectRangeFormat:H_,eventTimeRangeFormat:H_,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return n.format(r,"LT",t)+" – "},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – "+n.format(r,"LT",t)},timeGutterFormat:"LT",monthHeaderFormat:"MMMM YYYY",dayHeaderFormat:"dddd MMM DD",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"MMMM DD",t)+" – "+n.format(o,n.eq(r,o,"month")?"DD":"MMMM DD",t)},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"L",t)+" – "+n.format(o,"L",t)},agendaDateFormat:"ddd MMM DD",agendaTimeFormat:"LT",agendaTimeRangeFormat:H_};function B_(e){var t=e?e.toLowerCase():e;return"FullYear"===t?t="year":t||(t=void 0),t}var $_=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"t",t)+" – "+n.format(o,"t",t)},Y_={dateFormat:"dd",dayFormat:"dd EEE",weekdayFormat:"EEE",selectRangeFormat:$_,eventTimeRangeFormat:$_,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return n.format(r,"t",t)+" – "},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – "+n.format(r,"t",t)},timeGutterFormat:"t",monthHeaderFormat:"MMMM yyyy",dayHeaderFormat:"EEEE MMM dd",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"MMMM dd",t)+" – "+n.format(o,n.eq(r,o,"month")?"dd":"MMMM dd",t)},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"D",t)+" – "+n.format(o,"D",t)},agendaDateFormat:"EEE MMM dd",agendaTimeFormat:"t",agendaTimeRangeFormat:$_};function q_(e){var t=e?function(e){return/s$/.test(e)?e:e+"s"}(e.toLowerCase()):e;return"FullYear"===t?t="year":t||(t=void 0),t}var K_=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"t",t)+" – "+n.format(o,"t",t)},Q_={dateFormat:"dd",dayFormat:"ddd dd/MM",weekdayFormat:"ddd",selectRangeFormat:K_,eventTimeRangeFormat:K_,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return n.format(r,"t",t)+" – "},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – "+n.format(r,"t",t)},timeGutterFormat:"t",monthHeaderFormat:"Y",dayHeaderFormat:"dddd MMM dd",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"MMM dd",t)+" – "+n.format(o,Ve(r,o,"month")?"dd":"MMM dd",t)},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"d",t)+" – "+n.format(o,"d",t)},agendaDateFormat:"ddd MMM dd",agendaTimeFormat:"t",agendaTimeRangeFormat:K_};function G_(e){return new Rt({firstOfWeek:function(t){return(t=function(t){return t?e.findClosestCulture(t):e.culture()}(t))&&t.calendar.firstDay||0},formats:Q_,format:function(t,n,r){return e.format(t,n,r)}})}var X_=function(e,t,n){var r=e.start,o=e.end;return n.format(r,{time:"short"},t)+" – "+n.format(o,{time:"short"},t)},J_={dateFormat:"dd",dayFormat:"eee dd/MM",weekdayFormat:"eee",selectRangeFormat:X_,eventTimeRangeFormat:X_,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return n.format(r,{time:"short"},t)+" – "},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – "+n.format(r,{time:"short"},t)},timeGutterFormat:{time:"short"},monthHeaderFormat:"MMMM yyyy",dayHeaderFormat:"eeee MMM dd",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"MMM dd",t)+" – "+n.format(o,Ve(r,o,"month")?"dd":"MMM dd",t)},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,{date:"short"},t)+" – "+n.format(o,{date:"short"},t)},agendaDateFormat:"eee MMM dd",agendaTimeFormat:{time:"short"},agendaTimeRangeFormat:X_};var Z_=function(e,t,n){var r=e.start,o=e.end;return"".concat(n.format(r,"p",t)," – ").concat(n.format(o,"p",t))},ex={dateFormat:"dd",dayFormat:"dd eee",weekdayFormat:"ccc",selectRangeFormat:Z_,eventTimeRangeFormat:Z_,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return"".concat(n.format(r,"h:mma",t)," – ")},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – ".concat(n.format(r,"h:mma",t))},timeGutterFormat:"p",monthHeaderFormat:"MMMM yyyy",dayHeaderFormat:"cccc MMM dd",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return"".concat(n.format(r,"MMMM dd",t)," – ").concat(n.format(o,Ve(r,o,"month")?"dd":"MMMM dd",t))},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return"".concat(n.format(r,"P",t)," – ").concat(n.format(o,"P",t))},agendaDateFormat:"ccc MMM dd",agendaTimeFormat:"p",agendaTimeRangeFormat:Z_},tx={},nx={get exports(){return tx},set exports(e){tx=e}};!function(e,t){e.exports=function(e,t,n){t.prototype.isBetween=function(e,t,r,o){var a=n(e),i=n(t),l="("===(o=o||"()")[0],u=")"===o[1];return(l?this.isAfter(a,r):!this.isBefore(a,r))&&(u?this.isBefore(i,r):!this.isAfter(i,r))||(l?this.isBefore(a,r):!this.isAfter(a,r))&&(u?this.isAfter(i,r):!this.isBefore(i,r))}}}(nx);var rx=tx,ox={},ax={get exports(){return ox},set exports(e){ox=e}};!function(e,t){e.exports=function(e,t){t.prototype.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)}}}(ax);var ix=ox,lx={},ux={get exports(){return lx},set exports(e){lx=e}};!function(e,t){e.exports=function(e,t){t.prototype.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)}}}(ux);var sx=lx,cx={},fx={get exports(){return cx},set exports(e){cx=e}};!function(e,t){e.exports=function(e,t,n){var r=t.prototype,o=function(e){return e&&(e.indexOf?e:e.s)},a=function(e,t,n,r,a){var i=e.name?e:e.$locale(),l=o(i[t]),u=o(i[n]),s=l||u.map((function(e){return e.slice(0,r)}));if(!a)return s;var c=i.weekStart;return s.map((function(e,t){return s[(t+(c||0))%7]}))},i=function(){return n.Ls[n.locale()]},l=function(e,t){return e.formats[t]||function(e){return e.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}(e.formats[t.toUpperCase()])},u=function(){var e=this;return{months:function(t){return t?t.format("MMMM"):a(e,"months")},monthsShort:function(t){return t?t.format("MMM"):a(e,"monthsShort","months",3)},firstDayOfWeek:function(){return e.$locale().weekStart||0},weekdays:function(t){return t?t.format("dddd"):a(e,"weekdays")},weekdaysMin:function(t){return t?t.format("dd"):a(e,"weekdaysMin","weekdays",2)},weekdaysShort:function(t){return t?t.format("ddd"):a(e,"weekdaysShort","weekdays",3)},longDateFormat:function(t){return l(e.$locale(),t)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};r.localeData=function(){return u.bind(this)()},n.localeData=function(){var e=i();return{firstDayOfWeek:function(){return e.weekStart||0},weekdays:function(){return n.weekdays()},weekdaysShort:function(){return n.weekdaysShort()},weekdaysMin:function(){return n.weekdaysMin()},months:function(){return n.months()},monthsShort:function(){return n.monthsShort()},longDateFormat:function(t){return l(e,t)},meridiem:e.meridiem,ordinal:e.ordinal}},n.months=function(){return a(i(),"months")},n.monthsShort=function(){return a(i(),"monthsShort","months",3)},n.weekdays=function(e){return a(i(),"weekdays",null,null,e)},n.weekdaysShort=function(e){return a(i(),"weekdaysShort","weekdays",3,e)},n.weekdaysMin=function(e){return a(i(),"weekdaysMin","weekdays",2,e)}}}(fx);var dx=cx,px={},vx={get exports(){return px},set exports(e){px=e}};!function(e,t){var n;e.exports=(n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},function(e,t,r){var o=t.prototype,a=o.format;r.en.formats=n,o.format=function(e){void 0===e&&(e="YYYY-MM-DDTHH:mm:ssZ");var t=this.$locale().formats,r=function(e,t){return e.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(e,r,o){var a=o&&o.toUpperCase();return r||t[o]||n[o]||t[a].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))}(e,void 0===t?{}:t);return a.call(this,r)}})}(vx);var hx=px,mx={},gx={get exports(){return mx},set exports(e){mx=e}};!function(e,t){e.exports=function(e,t,n){var r=function(e,t){if(!t||!t.length||1===t.length&&!t[0]||1===t.length&&Array.isArray(t[0])&&!t[0].length)return null;var n;1===t.length&&t[0].length>0&&(t=t[0]),n=(t=t.filter((function(e){return e})))[0];for(var r=1;r<t.length;r+=1)t[r].isValid()&&!t[r][e](n)||(n=t[r]);return n};n.max=function(){var e=[].slice.call(arguments,0);return r("isAfter",e)},n.min=function(){var e=[].slice.call(arguments,0);return r("isBefore",e)}}}(gx);var yx=mx,bx={},wx={get exports(){return bx},set exports(e){bx=e}};!function(e,t){e.exports=function(){var e="minute",t=/[+-]\d\d(?::?\d\d)?/g,n=/([+-]|\d\d)/g;return function(r,o,a){var i=o.prototype;a.utc=function(e){return new o({date:e,utc:!0,args:arguments})},i.utc=function(t){var n=a(this.toDate(),{locale:this.$L,utc:!0});return t?n.add(this.utcOffset(),e):n},i.local=function(){return a(this.toDate(),{locale:this.$L,utc:!1})};var l=i.parse;i.parse=function(e){e.utc&&(this.$u=!0),this.$utils().u(e.$offset)||(this.$offset=e.$offset),l.call(this,e)};var u=i.init;i.init=function(){if(this.$u){var e=this.$d;this.$y=e.getUTCFullYear(),this.$M=e.getUTCMonth(),this.$D=e.getUTCDate(),this.$W=e.getUTCDay(),this.$H=e.getUTCHours(),this.$m=e.getUTCMinutes(),this.$s=e.getUTCSeconds(),this.$ms=e.getUTCMilliseconds()}else u.call(this)};var s=i.utcOffset;i.utcOffset=function(r,o){var a=this.$utils().u;if(a(r))return this.$u?0:a(this.$offset)?s.call(this):this.$offset;if("string"==typeof r&&(r=function(e){void 0===e&&(e="");var r=e.match(t);if(!r)return null;var o=(""+r[0]).match(n)||["-",0,0],a=o[0],i=60*+o[1]+ +o[2];return 0===i?0:"+"===a?i:-i}(r),null===r))return this;var i=Math.abs(r)<=16?60*r:r,l=this;if(o)return l.$offset=i,l.$u=0===r,l;if(0!==r){var u=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(l=this.local().add(i+u,e)).$offset=i,l.$x.$localOffset=u}else l=this.utc();return l};var c=i.format;i.format=function(e){var t=e||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return c.call(this,t)},i.valueOf=function(){var e=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*e},i.isUTC=function(){return!!this.$u},i.toISOString=function(){return this.toDate().toISOString()},i.toString=function(){return this.toDate().toUTCString()};var f=i.toDate;i.toDate=function(e){return"s"===e&&this.$offset?a(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():f.call(this)};var d=i.diff;i.diff=function(e,t,n){if(e&&this.$u===e.$u)return d.call(this,e,t,n);var r=this.local(),o=a(e).local();return d.call(r,o,t,n)}}}()}(wx);var kx=bx,Ex=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"LT",t)+" – "+n.format(o,"LT",t)},Sx={dateFormat:"DD",dayFormat:"DD ddd",weekdayFormat:"ddd",selectRangeFormat:Ex,eventTimeRangeFormat:Ex,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return n.format(r,"LT",t)+" – "},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – "+n.format(r,"LT",t)},timeGutterFormat:"LT",monthHeaderFormat:"MMMM YYYY",dayHeaderFormat:"dddd MMM DD",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"MMMM DD",t)+" – "+n.format(o,n.eq(r,o,"month")?"DD":"MMMM DD",t)},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return n.format(r,"L",t)+" – "+n.format(o,"L",t)},agendaDateFormat:"ddd MMM DD",agendaTimeFormat:"LT",agendaTimeRangeFormat:Ex};function Dx(e){var t=e?e.toLowerCase():e;return"FullYear"===t?t="year":t||(t=void 0),t}var _x={eventWrapper:t,timeSlotWrapper:t,dateCellWrapper:t};e.Calendar=W_,e.DateLocalizer=Rt,e.Navigate=Se,e.Views=De,e.components=_x,e.dateFnsLocalizer=function(e){var t=e.startOfWeek,n=e.getDay,r=e.format,o=e.locales;return new Rt({formats:ex,firstOfWeek:function(e){return n(t(new Date,{locale:o[e]}))},format:function(e,t,n){return r(new Date(e),t,{locale:o[n]})}})},e.dayjsLocalizer=function(e){e.extend(rx),e.extend(ix),e.extend(sx),e.extend(dx),e.extend(hx),e.extend(yx),e.extend(kx);var t=e.tz?e.tz:e;function n(n,r){var o,a=t(n),i=t(r);if(!t.tz)return a.toDate().getTimezoneOffset()-i.toDate().getTimezoneOffset();var l=null!==(o=a.tz().$x.$timezone)&&void 0!==o?o:e.tz.guess();return-t.tz(+a,l).utcOffset()- -t.tz(+i,l).utcOffset()}function r(e,n,r){var o=Dx(r);return[o?t(e).startOf(o):t(e),o?t(n).startOf(o):t(n),o]}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=Dx(arguments.length>1?arguments[1]:void 0);return n?t(e).startOf(n).toDate():t(e).toDate()}function a(e,t,n){var o=E(r(e,t,n),3),a=o[0],i=o[1],l=o[2];return a.isSame(i,l)}function i(e,t,n){var o=E(r(e,t,n),3),a=o[0],i=o[1],l=o[2];return a.isSameOrBefore(i,l)}function l(e,n,r){var o=Dx(r);return t(e).add(n,o).toDate()}function u(e,n){var r=Dx(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day"),o=t(e);return t(n).diff(o,r)}function s(e){return t(e).startOf("month").startOf("week").toDate()}function c(e){return t(e).endOf("month").endOf("week").toDate()}function f(e,n){var r=t(e);return t(n).diff(r,"day")}return new Rt({formats:Sx,firstOfWeek:function(t){var n=t?e.localeData(t):e.localeData();return n?n.firstDayOfWeek():0},firstVisibleDay:s,lastVisibleDay:c,visibleDays:function(e){for(var t=s(e),n=c(e),r=[];i(t,n);)r.push(t),t=l(t,1,"d");return r},format:function(e,n,r){return function(e,t){return t?e.locale(t):e}(t(e),r).format(n)},lt:function(e,t,n){var o=E(r(e,t,n),3),a=o[0],i=o[1],l=o[2];return a.isBefore(i,l)},lte:i,gt:function(e,t,n){var o=E(r(e,t,n),3),a=o[0],i=o[1],l=o[2];return a.isAfter(i,l)},gte:function(e,t,n){var o=E(r(e,t,n),3),a=o[0],i=o[1],l=o[2];return a.isSameOrBefore(i,l)},eq:a,neq:function(e,t,n){return!a(e,t,n)},merge:function(n,r){if(!n&&!r)return null;var o=t(r).format("HH:mm:ss"),a=t(n).startOf("day").format("MM/DD/YYYY");return e("".concat(a," ").concat(o),"MM/DD/YYYY HH:mm:ss").toDate()},inRange:function(e,n,r){var o=Dx(arguments.length>3&&void 0!==arguments[3]?arguments[3]:"day"),a=t(e),i=t(n),l=t(r);return a.isBetween(i,l,o,"[]")},startOf:o,endOf:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=Dx(arguments.length>1?arguments[1]:void 0);return n?t(e).endOf(n).toDate():t(e).toDate()},range:function(e,n){for(var r=Dx(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day"),o=t(e).toDate(),a=[];i(o,n);)a.push(o),o=l(o,1,r);return a},add:l,diff:u,ceil:function(e,t){var n=Dx(t),r=o(e,n);return a(r,e)?r:l(r,1,n)},min:function(n,r){var o=t(n),a=t(r);return e.min(o,a).toDate()},max:function(n,r){var o=t(n),a=t(r);return e.max(o,a).toDate()},minutes:function(e){return t(e).minutes()},getSlotDate:function(e,n,r){return t(e).startOf("day").minute(n+r).toDate()},getTimezoneOffset:function(e){return t(e).toDate().getTimezoneOffset()},getDstOffset:n,getTotalMin:function(e,t){return u(e,t,"minutes")},getMinutesFromMidnight:function(e){var r=t(e).startOf("day");return t(e).diff(r,"minutes")+function(e){return n(t(e).startOf("day"),e)}(e)},continuesPrior:function(e,n){var r=t(e),o=t(n);return r.isBefore(o,"day")},continuesAfter:function(e,n,r){var o=t(n),a=t(r);return o.isSameOrAfter(a,"minutes")},sortEvents:function(e){var t=e.evtA,n=t.start,r=t.end,a=t.allDay,i=e.evtB,l=i.start,u=i.end,s=i.allDay,c=+o(n,"day")-+o(l,"day"),d=f(n,r),p=f(l,u);return c||p-d||!!s-!!a||+n-+l||+r-+u},inEventRange:function(e){var n=e.event,r=n.start,o=n.end,a=e.range,i=a.start,l=a.end,u=t(r).startOf("day"),s=t(o),c=t(i),f=t(l),d=u.isSameOrBefore(f,"day"),p=!u.isSame(s,"minutes")?s.isAfter(c,"minutes"):s.isSameOrAfter(c,"minutes");return d&&p},isSameDate:function(e,n){var r=t(e),o=t(n);return r.isSame(o,"day")},browserTZOffset:function(){var e=new Date,n=/-/.test(e.toString())?"-":"",r=e.getTimezoneOffset(),o=Number("".concat(n).concat(Math.abs(r)));return t().utcOffset()>o?1:0}})},e.globalizeLocalizer=function(e){var t=function(t){return t?e(t):e};return e.load?new Rt({firstOfWeek:function(e){try{var n=t(e).cldr,r=n.attributes.territory,o=n.get("supplemental").weekData.firstDay[r||"001"];return["sun","mon","tue","wed","thu","fri","sat"].indexOf(o)}catch(n){var a=new Date,i=Math.max(parseInt(t(e).formatDate(a,{raw:"e"}),10)-1,0);return Math.abs(a.getDay()-i)}},formats:J_,format:function(e,n,r){return n="string"==typeof n?{raw:n}:n,t(r).formatDate(e,n)}}):G_(e)},e.luxonLocalizer=function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).firstDayOfWeek,n=void 0===t?7:t;function r(t,n,r){var o=q_(r);return[o?e.fromJSDate(t).startOf(o):e.fromJSDate(t),o?e.fromJSDate(n).startOf(o):e.fromJSDate(n),o]}function a(e){var t=e.weekday;if(t===n)return e.startOf("day");if(1===n)return e.startOf("week");var r=7===n?t:t+(7-n);return e.minus({day:r}).startOf("day")}function i(e){var t=e.weekday,r=1===n?7:n-1;return t===r?e.endOf("day"):1===n?e.endOf("week"):(n>r?e.plus({day:n-r}):e).set({weekday:r}).endOf("day")}function l(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,n=q_(arguments.length>1?arguments[1]:void 0);if(n){var r=e.fromJSDate(t);return n.includes("week")?a(r):r.startOf(n)}return e.fromJSDate(t)}function u(){return l(arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,arguments.length>1?arguments[1]:void 0).toJSDate()}function s(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,n=q_(arguments.length>1?arguments[1]:void 0);if(n){var r=e.fromJSDate(t);return n.includes("week")?i(r):r.endOf(n)}return e.fromJSDate(t)}function c(e,t,n){var o=E(r(e,t,n),2);return+o[0]==+o[1]}function f(e,t,n){return!c(e,t,n)}function d(e,t,n){var o=E(r(e,t,n),2);return+o[0]>+o[1]}function p(e,t,n){var o=E(r(e,t,n),2);return+o[0]<+o[1]}function v(e,t,n){var o=E(r(e,t,n),2);return+o[0]>=+o[1]}function h(e,t,n){var o=E(r(e,t,n),2);return+o[0]<=+o[1]}function m(t,n,r){var a=q_(r);return e.fromJSDate(t).plus(o({},a,n)).toJSDate()}function g(t,n){var r=q_(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day"),o=e.fromJSDate(t),a=e.fromJSDate(n);return Math.floor(a.diff(o,r,{conversionAccuracy:"longterm"}).toObject()[r])}function y(e){return a(l(e,"month")).toJSDate()}function b(e){return i(s(e,"month")).toJSDate()}function w(t,n){var r=e.fromJSDate(t);return e.fromJSDate(n).diff(r).as("days")}return new Rt({format:function(t,n,r){return r?function(t,n,r){return e.fromJSDate(t).setLocale(n).toFormat(r)}(t,r,n):function(t,n){return e.fromJSDate(t).toFormat(n)}(t,n)},formats:Y_,firstOfWeek:function(){return n},firstVisibleDay:y,lastVisibleDay:b,visibleDays:function(e){for(var t=y(e),n=b(e),r=[];h(t,n);)r.push(t),t=m(t,1,"day");return r},lt:p,lte:h,gt:d,gte:v,eq:c,neq:f,merge:function(t,n){if(!t&&!n)return null;var r=e.fromJSDate(n);return l(t,"day").set({hour:r.hour,minute:r.minute,second:r.second,millisecond:r.millisecond}).toJSDate()},inRange:function(e,t,n){var r=q_(arguments.length>3&&void 0!==arguments[3]?arguments[3]:"day"),o=l(e,r),a=l(t,r),i=l(n,r);return+o>=+a&&+o<=+i},startOf:u,endOf:function(){return s(arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,arguments.length>1?arguments[1]:void 0).toJSDate()},range:function(t,n){for(var r=q_(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day"),o=e.fromJSDate(t).toJSDate(),a=[];h(o,n);)a.push(o),o=m(o,1,r);return a},add:m,diff:g,ceil:function(e,t){var n=q_(t),r=u(e,n);return c(r,e)?r:m(r,1,n)},min:function(t,n){var r=e.fromJSDate(t),o=e.fromJSDate(n);return e.min(r,o).toJSDate()},max:function(t,n){var r=e.fromJSDate(t),o=e.fromJSDate(n);return e.max(r,o).toJSDate()},getSlotDate:function(e,t,n){return l(e,"day").set({minutes:t+n}).toJSDate()},getTotalMin:function(e,t){return g(e,t,"minutes")},getMinutesFromMidnight:function(t){var n=l(t,"day"),r=e.fromJSDate(t);return Math.round(r.diff(n,"minutes",{conversionAccuracy:"longterm"}).toObject().minutes)},continuesPrior:function(e,t){return p(e,t)},continuesAfter:function(e,t,n){return v(t,n)},sortEvents:function(e){var t=e.evtA,n=t.start,r=t.end,o=t.allDay,a=e.evtB,i=a.start,l=a.end,s=a.allDay,c=+u(n,"day")-+u(i,"day"),f=w(n,r),d=w(i,l);return c||d-f||!!s-!!o||+n-+i||+r-+l},inEventRange:function(e){var t=e.event,n=t.start,r=t.end,o=e.range,a=o.start,i=o.end,l=u(n,"day"),s=h(l,i,"day"),c=f(l,r,"minutes")?d(r,a,"minutes"):v(r,a,"minutes");return s&&c},isSameDate:function(t,n){var r=e.fromJSDate(t),o=e.fromJSDate(n);return r.hasSame(o,"day")},daySpan:w,browserTZOffset:function(){var t=new Date,n=/-/.test(t.toString())?"-":"",r=t.getTimezoneOffset(),o=Number("".concat(n).concat(Math.abs(r)));return e.local().offset>o?1:0}})},e.momentLocalizer=function(e){function t(t,n){var r,o,a=e(t).local(),i=e(n).local();if(!e.tz)return a.toDate().getTimezoneOffset()-i.toDate().getTimezoneOffset();var l=null!==(r=null==a||null===(o=a._z)||void 0===o?void 0:o.name)&&void 0!==r?r:e.tz.guess();return e.tz.zone(l).utcOffset(+a)-e.tz.zone(l).utcOffset(+i)}function n(t,n,r){var o=B_(r);return[o?e(t).startOf(o):e(t),o?e(n).startOf(o):e(n),o]}function r(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=B_(arguments.length>1?arguments[1]:void 0);return n?e(t).startOf(n).toDate():e(t).toDate()}function o(e,t,r){var o=E(n(e,t,r),3),a=o[0],i=o[1],l=o[2];return a.isSame(i,l)}function a(e,t,r){var o=E(n(e,t,r),3),a=o[0],i=o[1],l=o[2];return a.isSameOrBefore(i,l)}function i(t,n,r){var o=B_(r);return e(t).add(n,o).toDate()}function l(t,n){var r=B_(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day"),o=e(t);return e(n).diff(o,r)}function u(t){return e(t).startOf("month").startOf("week").toDate()}function s(t){return e(t).endOf("month").endOf("week").toDate()}function c(t,n){var r=e(t),o=e(n);return e.duration(o.diff(r)).days()}return new Rt({formats:V_,firstOfWeek:function(t){var n=t?e.localeData(t):e.localeData();return n?n.firstDayOfWeek():0},firstVisibleDay:u,lastVisibleDay:s,visibleDays:function(e){for(var t=u(e),n=s(e),r=[];a(t,n);)r.push(t),t=i(t,1,"d");return r},format:function(t,n,r){return function(e,t){return t?e.locale(t):e}(e(t),r).format(n)},lt:function(e,t,r){var o=E(n(e,t,r),3),a=o[0],i=o[1],l=o[2];return a.isBefore(i,l)},lte:a,gt:function(e,t,r){var o=E(n(e,t,r),3),a=o[0],i=o[1],l=o[2];return a.isAfter(i,l)},gte:function(e,t,r){var o=E(n(e,t,r),3),a=o[0],i=o[1],l=o[2];return a.isSameOrBefore(i,l)},eq:o,neq:function(e,t,n){return!o(e,t,n)},merge:function(t,n){if(!t&&!n)return null;var r=e(n).format("HH:mm:ss"),o=e(t).startOf("day").format("MM/DD/YYYY");return e("".concat(o," ").concat(r),"MM/DD/YYYY HH:mm:ss").toDate()},inRange:function(t,n,r){var o=B_(arguments.length>3&&void 0!==arguments[3]?arguments[3]:"day"),a=e(t),i=e(n),l=e(r);return a.isBetween(i,l,o,"[]")},startOf:r,endOf:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=B_(arguments.length>1?arguments[1]:void 0);return n?e(t).endOf(n).toDate():e(t).toDate()},range:function(t,n){for(var r=B_(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day"),o=e(t).toDate(),l=[];a(o,n);)l.push(o),o=i(o,1,r);return l},add:i,diff:l,ceil:function(e,t){var n=B_(t),a=r(e,n);return o(a,e)?a:i(a,1,n)},min:function(t,n){var r=e(t),o=e(n);return e.min(r,o).toDate()},max:function(t,n){var r=e(t),o=e(n);return e.max(r,o).toDate()},minutes:function(t){return e(t).minutes()},getSlotDate:function(t,n,r){return e(t).startOf("day").minute(n+r).toDate()},getTimezoneOffset:function(t){return e(t).toDate().getTimezoneOffset()},getDstOffset:t,getTotalMin:function(e,t){return l(e,t,"minutes")},getMinutesFromMidnight:function(n){var r=e(n).startOf("day");return e(n).diff(r,"minutes")+function(n){return t(e(n).startOf("day"),n)}(n)},continuesPrior:function(t,n){var r=e(t),o=e(n);return r.isBefore(o,"day")},continuesAfter:function(t,n,r){var o=e(n),a=e(r);return o.isSameOrAfter(a,"minutes")},sortEvents:function(e){var t=e.evtA,n=t.start,o=t.end,a=t.allDay,i=e.evtB,l=i.start,u=i.end,s=i.allDay,f=+r(n,"day")-+r(l,"day"),d=c(n,o),p=c(l,u);return f||p-d||!!s-!!a||+n-+l||+o-+u},inEventRange:function(t){var n=t.event,r=n.start,o=n.end,a=t.range,i=a.start,l=a.end,u=e(r).startOf("day"),s=e(o),c=e(i),f=e(l),d=u.isSameOrBefore(f,"day"),p=!u.isSame(s,"minutes")?s.isAfter(c,"minutes"):s.isSameOrAfter(c,"minutes");return d&&p},isSameDate:function(t,n){var r=e(t),o=e(n);return r.isSame(o,"day")},daySpan:c,browserTZOffset:function(){var t=new Date,n=/-/.test(t.toString())?"-":"",r=t.getTimezoneOffset(),o=Number("".concat(n).concat(Math.abs(r)));return e().utcOffset()>o?1:0}})},e.move=WE}));
