#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../@typescript-eslint/typescript-estree/node_modules/semver/bin/semver.js" "$@"
  ret=$?
else 
  node  "$basedir/../@typescript-eslint/typescript-estree/node_modules/semver/bin/semver.js" "$@"
  ret=$?
fi
exit $ret
