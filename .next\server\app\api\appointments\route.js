"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/appointments/route";
exports.ids = ["app/api/appointments/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_appointments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/appointments/route.ts */ \"(rsc)/./src/app/api/appointments/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/appointments/route\",\n        pathname: \"/api/appointments\",\n        filename: \"route\",\n        bundlePath: \"app/api/appointments/route\"\n    },\n    resolvedPagePath: \"D:\\\\CURSOR-AI\\\\ai-project\\\\TintPlatesStdio\\\\src\\\\app\\\\api\\\\appointments\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_CURSOR_AI_ai_project_TintPlatesStdio_src_app_api_appointments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/appointments/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/appointments/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/appointments/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)();\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        // Sadece öğretmen ve admin program ekleyebilir\n        if (![\n            \"teacher\",\n            \"admin\"\n        ].includes(session.user.role)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Yetkisiz erişim\"\n            }, {\n                status: 403\n            });\n        }\n        const data = await request.json();\n        console.log(\"Gelen veri:\", data) // Debug için\n        ;\n        // Zorunlu alanları kontrol et\n        const requiredFields = [\n            \"studentId\",\n            \"teacherId\",\n            \"roomId\",\n            \"serviceId\",\n            \"packageId\",\n            \"date\",\n            \"startTime\"\n        ];\n        const missingFields = requiredFields.filter((field)=>!data[field]);\n        if (missingFields.length > 0) {\n            console.log(\"Eksik alanlar:\", missingFields) // Debug için\n            ;\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Şu alanlar eksik: ${missingFields.join(\", \")}`\n            }, {\n                status: 400\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        // ObjectId dönüşümlerini yap\n        const appointment = {\n            studentId: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.studentId),\n            teacherId: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.teacherId),\n            roomId: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.roomId),\n            serviceId: data.serviceId,\n            packageId: data.packageId,\n            date: new Date(data.date),\n            startTime: data.startTime,\n            endTime: data.endTime || addHourToTime(data.startTime, 1),\n            notes: data.notes || \"\",\n            isActive: true,\n            createdAt: new Date()\n        };\n        // Çakışma kontrolü\n        const existingAppointment = await db.collection(\"appointments\").findOne({\n            roomId: appointment.roomId,\n            date: appointment.date,\n            startTime: appointment.startTime,\n            isActive: true\n        });\n        if (existingAppointment) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Bu saat i\\xe7in se\\xe7ili odada başka bir randevu var\"\n            }, {\n                status: 400\n            });\n        }\n        // Randevuyu kaydet\n        const result = await db.collection(\"appointments\").insertOne(appointment);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            appointment: {\n                id: result.insertedId,\n                ...appointment\n            }\n        });\n    } catch (error) {\n        console.error(\"Randevu eklenirken hata:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)();\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        // Sadece öğretmen ve admin program güncelleyebilir\n        if (![\n            \"teacher\",\n            \"admin\"\n        ].includes(session.user.role)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Yetkisiz erişim\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID parametresi gerekli\"\n            }, {\n                status: 400\n            });\n        }\n        const data = await request.json();\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const updateData = {\n            ...data,\n            updatedAt: new Date()\n        };\n        if (data.studentId) updateData.studentId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.studentId);\n        if (data.teacherId) updateData.teacherId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.teacherId);\n        if (data.roomId) updateData.roomId = new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(data.roomId);\n        if (data.date) updateData.date = new Date(data.date);\n        const result = await db.collection(\"appointments\").updateOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: updateData\n        });\n        if (result.matchedCount === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Randevu bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Randevu g\\xfcncellendi\"\n        });\n    } catch (error) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID parametresi gerekli\"\n            }, {\n                status: 400\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        const result = await db.collection(\"appointments\").updateOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: {\n                isActive: false,\n                updatedAt: new Date()\n            }\n        });\n        if (result.matchedCount === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Randevu bulunamadı\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Randevu silindi\"\n        });\n    } catch (error) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nfunction addHourToTime(time, hours) {\n    const [hour, minute] = time.split(\":\").map(Number);\n    const newHour = (hour + hours) % 24;\n    return `${newHour.toString().padStart(2, \"0\")}:${minute.toString().padStart(2, \"0\")}`;\n}\nasync function GET() {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)();\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Oturum gerekli\"\n            }, {\n                status: 401\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"pilatesstudio\");\n        // Tüm aktif randevuları getir (hem öğretmen hem admin için)\n        const appointments = await db.collection(\"appointments\").aggregate([\n            {\n                $match: {\n                    isActive: true\n                }\n            },\n            {\n                $lookup: {\n                    from: \"teachers\",\n                    localField: \"teacherId\",\n                    foreignField: \"_id\",\n                    as: \"teacher\"\n                }\n            },\n            {\n                $lookup: {\n                    from: \"students\",\n                    localField: \"studentId\",\n                    foreignField: \"_id\",\n                    as: \"student\"\n                }\n            },\n            {\n                $unwind: \"$teacher\"\n            },\n            {\n                $unwind: \"$student\"\n            },\n            {\n                $project: {\n                    id: {\n                        $toString: \"$_id\"\n                    },\n                    studentName: \"$student.name\",\n                    teacherName: \"$teacher.name\",\n                    teacherColor: \"$teacher.color\",\n                    roomName: 1,\n                    serviceName: 1,\n                    date: 1,\n                    startTime: 1,\n                    endTime: 1,\n                    notes: 1\n                }\n            }\n        ]).toArray();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            appointments\n        });\n    } catch (error) {\n        console.error(\"Randevular alınırken hata:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Randevular alınamadı\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/appointments/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"Credentials\",\n            credentials: {\n                email: {\n                    label: \"E-posta\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Şifre\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error(\"E-posta ve şifre gerekli\");\n                    }\n                    const client = await _mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n                    const db = client.db(\"pilatesstudio\");\n                    // Önce users koleksiyonunda ara (admin için)\n                    let user = await db.collection(\"users\").findOne({\n                        email: credentials.email,\n                        isActive: true\n                    });\n                    // Kullanıcı bulunamazsa öğretmenler koleksiyonunda ara\n                    if (!user) {\n                        user = await db.collection(\"teachers\").findOne({\n                            email: credentials.email,\n                            isActive: true\n                        });\n                    }\n                    if (!user) {\n                        throw new Error(\"Kullanıcı bulunamadı\");\n                    }\n                    const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isValid) {\n                        throw new Error(\"Hatalı şifre\");\n                    }\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        role: user.role || \"teacher\",\n                        color: user.color // Öğretmenler için renk kodu\n                    };\n                } catch (error) {\n                    throw error;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                if (user.color) token.color = user.color; // Öğretmen renk kodu\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                if (token.color) session.user.color = token.color; // Öğretmen renk kodu\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/login\"\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"MONGODB_URI ortam değişkeni tanımlanmamış.\");\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {};\nlet client;\nlet clientPromise;\nif (true) {\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCURSOR-AI%5Cai-project%5CTintPlatesStdio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();