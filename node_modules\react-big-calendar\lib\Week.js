"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _toArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _callSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/callSuper"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _react = _interopRequireDefault(require("react"));
var _constants = require("./utils/constants");
var _propTypes = require("./utils/propTypes");
var _TimeGrid = _interopRequireDefault(require("./TimeGrid"));
var _excluded = ["date", "localizer", "min", "max", "scrollToTime", "enableAutoScroll"];
var Week = /*#__PURE__*/function (_React$Component) {
  function Week() {
    (0, _classCallCheck2.default)(this, Week);
    return (0, _callSuper2.default)(this, Week, arguments);
  }
  (0, _inherits2.default)(Week, _React$Component);
  return (0, _createClass2.default)(Week, [{
    key: "render",
    value: function render() {
      /**
       * This allows us to default min, max, and scrollToTime
       * using our localizer. This is necessary until such time
       * as TimeGrid is converted to a functional component.
       */
      var _this$props = this.props,
        date = _this$props.date,
        localizer = _this$props.localizer,
        _this$props$min = _this$props.min,
        min = _this$props$min === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$min,
        _this$props$max = _this$props.max,
        max = _this$props$max === void 0 ? localizer.endOf(new Date(), 'day') : _this$props$max,
        _this$props$scrollToT = _this$props.scrollToTime,
        scrollToTime = _this$props$scrollToT === void 0 ? localizer.startOf(new Date(), 'day') : _this$props$scrollToT,
        _this$props$enableAut = _this$props.enableAutoScroll,
        enableAutoScroll = _this$props$enableAut === void 0 ? true : _this$props$enableAut,
        props = (0, _objectWithoutProperties2.default)(_this$props, _excluded);
      var range = Week.range(date, this.props);
      return /*#__PURE__*/_react.default.createElement(_TimeGrid.default, Object.assign({}, props, {
        range: range,
        eventOffset: 15,
        localizer: localizer,
        min: min,
        max: max,
        scrollToTime: scrollToTime,
        enableAutoScroll: enableAutoScroll
      }));
    }
  }]);
}(_react.default.Component);
Week.defaultProps = _TimeGrid.default.defaultProps;
Week.navigate = function (date, action, _ref) {
  var localizer = _ref.localizer;
  switch (action) {
    case _constants.navigate.PREVIOUS:
      return localizer.add(date, -1, 'week');
    case _constants.navigate.NEXT:
      return localizer.add(date, 1, 'week');
    default:
      return date;
  }
};
Week.range = function (date, _ref2) {
  var localizer = _ref2.localizer;
  var firstOfWeek = localizer.startOfWeek();
  var start = localizer.startOf(date, 'week', firstOfWeek);
  var end = localizer.endOf(date, 'week', firstOfWeek);
  return localizer.range(start, end);
};
Week.title = function (date, _ref3) {
  var localizer = _ref3.localizer;
  var _Week$range = Week.range(date, {
      localizer: localizer
    }),
    _Week$range2 = (0, _toArray2.default)(_Week$range),
    start = _Week$range2[0],
    rest = _Week$range2.slice(1);
  return localizer.format({
    start: start,
    end: rest.pop()
  }, 'dayRangeHeaderFormat');
};
var _default = exports.default = Week;