!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):e.clsx=n()}(this,(function(){function e(n){var t,f,o="";if("string"==typeof n||"number"==typeof n)o+=n;else if("object"==typeof n)if(Array.isArray(n))for(t=0;t<n.length;t++)n[t]&&(f=e(n[t]))&&(o&&(o+=" "),o+=f);else for(t in n)n[t]&&(o&&(o+=" "),o+=t);return o}function n(){for(var n,t,f=0,o="";f<arguments.length;)(n=arguments[f++])&&(t=e(n))&&(o&&(o+=" "),o+=t);return o}return n.clsx=n,n}));