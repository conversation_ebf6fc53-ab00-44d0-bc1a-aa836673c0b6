"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var ResourceHeader = function ResourceHeader(_ref) {
  var label = _ref.label;
  return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, label);
};
var _default = exports.default = ResourceHeader;