@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Dancing+Script:wght@400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-body text-secondary-800 bg-gradient-to-br from-primary-50 via-white to-secondary-50;
    background-attachment: fixed;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }
}

@layer components {
  /* Dans/Bale Temalı Buton Stilleri */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700
           text-white font-medium px-6 py-3 rounded-xl shadow-soft hover:shadow-medium
           transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-100 to-secondary-200 hover:from-secondary-200 hover:to-secondary-300
           text-secondary-700 font-medium px-6 py-3 rounded-xl shadow-soft hover:shadow-medium
           transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-elegant {
    @apply bg-gradient-to-r from-elegant-400 to-accent-400 hover:from-elegant-500 hover:to-accent-500
           text-white font-medium px-6 py-3 rounded-xl shadow-soft hover:shadow-medium
           transform hover:scale-105 transition-all duration-300 ease-out;
  }

  /* Zarif Kart Stilleri */
  .card-elegant {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft hover:shadow-medium
           border border-primary-100/50 transition-all duration-300 ease-out
           hover:transform hover:scale-[1.02];
  }

  .card-dance {
    @apply bg-gradient-to-br from-white via-primary-50/30 to-secondary-50/30
           rounded-2xl shadow-soft hover:shadow-large border border-primary-200/30
           transition-all duration-500 ease-out hover:transform hover:scale-[1.02]
           backdrop-blur-sm;
  }

  /* Form Stilleri */
  .input-elegant {
    @apply w-full px-4 py-3 rounded-xl border-2 border-secondary-200
           focus:border-primary-400 focus:ring-4 focus:ring-primary-100
           transition-all duration-300 ease-out bg-white/80 backdrop-blur-sm
           placeholder-secondary-400 text-secondary-700;
  }

  /* Navigasyon Stilleri */
  .nav-item {
    @apply flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-300 ease-out
           hover:bg-primary-50 hover:text-primary-700 hover:transform hover:scale-105;
  }

  .nav-item-active {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 shadow-soft;
  }

  /* Özel Scrollbar */
  .scrollbar-elegant::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-elegant::-webkit-scrollbar-track {
    @apply bg-secondary-100 rounded-full;
  }

  .scrollbar-elegant::-webkit-scrollbar-thumb {
    @apply bg-primary-300 rounded-full hover:bg-primary-400;
  }
}